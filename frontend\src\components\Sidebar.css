.sidebar {
  width: 250px;
  background-color: var(--ust-white);
  border-right: 1px solid var(--ust-border);
  position: fixed;
  left: 0;
  top: 60px;
  height: calc(100vh - 60px);
  overflow-y: auto;
  z-index: 999;
}

.sidebar-content {
  display: flex;
  flex-direction: column;
  height: 100%;
  padding: 20px 0;
}

.sidebar-nav {
  flex: 1;
}

.nav-section {
  margin-bottom: 30px;
  padding: 0 20px;
}

.nav-section-title {
  color: var(--ust-dark-teal);
  font-size: 12px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  margin-bottom: 15px;
  padding-bottom: 5px;
  border-bottom: 1px solid var(--ust-border);
}

.nav-list {
  list-style: none;
  margin: 0;
  padding: 0;
}

.nav-item {
  margin-bottom: 5px;
}

.nav-link {
  display: flex;
  align-items: center;
  padding: 12px 15px;
  color: var(--ust-soft-black);
  text-decoration: none;
  border-radius: 8px;
  transition: all 0.3s ease;
  font-size: 14px;
  font-weight: 500;
}

.nav-link:hover {
  background-color: var(--ust-gray);
  color: var(--ust-dark-teal);
}

.nav-link.active {
  background-color: var(--ust-light-teal);
  color: var(--ust-white);
}

.nav-icon {
  margin-right: 10px;
  font-size: 16px;
}

.nav-label {
  flex: 1;
}

.nav-count {
  font-size: 12px;
  color: var(--ust-light-teal);
  font-weight: 600;
}

.nav-link.active .nav-count {
  color: rgba(255, 255, 255, 0.8);
}

.nav-button {
  display: flex;
  align-items: center;
  width: 100%;
  padding: 12px 15px;
  border: none;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
}

.nav-button .nav-icon {
  margin-right: 10px;
  font-size: 16px;
}

.sidebar-footer {
  padding: 20px;
  border-top: 1px solid var(--ust-border);
}

.system-info {
  background-color: var(--ust-gray);
  padding: 15px;
  border-radius: 8px;
}

.info-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
  font-size: 12px;
}

.info-item:last-child {
  margin-bottom: 0;
}

.info-label {
  color: var(--ust-soft-black);
  font-weight: 500;
}

.info-value {
  font-weight: 600;
}

@media (max-width: 768px) {
  .sidebar {
    transform: translateX(-100%);
    transition: transform 0.3s ease;
  }
  
  .sidebar.open {
    transform: translateX(0);
  }
}

#!/usr/bin/env python3
"""
Test the new single-page "What If" interface
"""

import requests
import json
import time

def test_new_interface():
    print('=== TESTING NEW SINGLE-PAGE "WHAT IF" INTERFACE ===')

    # Test 1: Basic calculation with default parameters
    print('\n1. Testing basic calculation...')
    calc_response = requests.post('http://localhost:8001/calculate-battery-life', json={
        'temperature': 20.0,
        'tx_power_dbm': 14,
        'tx_interval_minutes': 15,
        'spreading_factor': 'SF7',
        'sleep_mode': 'stop2',
        'sensor_resolution_bits': 12,
        'measurements_per_tx': 1,
        'led_enabled': True,
        'battery_count': 2
    })
    
    if calc_response.status_code == 200:
        result = calc_response.json()
        print(f'✅ Basic calculation successful')
        print(f'   Battery life: {result["battery_life_years"]:.1f} years')
        print(f'   TX current: {result["lora_details"]["tx_current_ma"]:.1f} mA')
        print(f'   Sleep current: {result["lora_details"]["sleep_current_ua"]:.1f} µA')
    else:
        print(f'❌ Basic calculation failed: {calc_response.status_code}')
        return False

    # Test 2: Parameter variation - TX Power
    print('\n2. Testing TX power variation...')
    power_levels = [7, 14, 20]
    for power in power_levels:
        calc_response = requests.post('http://localhost:8001/calculate-battery-life', json={
            'temperature': 20.0,
            'tx_power_dbm': power,
            'tx_interval_minutes': 15,
            'spreading_factor': 'SF7',
            'sleep_mode': 'stop2',
            'sensor_resolution_bits': 12,
            'measurements_per_tx': 1,
            'led_enabled': True,
            'battery_count': 2
        })
        
        if calc_response.status_code == 200:
            result = calc_response.json()
            print(f'   {power} dBm: {result["battery_life_years"]:.1f} years, {result["lora_details"]["tx_current_ma"]:.1f} mA')
        else:
            print(f'   ❌ {power} dBm calculation failed')

    # Test 3: Temperature variation
    print('\n3. Testing temperature variation...')
    temperatures = [-40, -20, 0, 20, 40]
    for temp in temperatures:
        calc_response = requests.post('http://localhost:8001/calculate-battery-life', json={
            'temperature': temp,
            'tx_power_dbm': 14,
            'tx_interval_minutes': 15,
            'spreading_factor': 'SF7',
            'sleep_mode': 'stop2',
            'sensor_resolution_bits': 12,
            'measurements_per_tx': 1,
            'led_enabled': True,
            'battery_count': 2
        })
        
        if calc_response.status_code == 200:
            result = calc_response.json()
            derating = result["battery_details"]["temperature_derating_factor"]
            print(f'   {temp}°C: {result["battery_life_years"]:.1f} years, {derating*100:.0f}% capacity')
        else:
            print(f'   ❌ {temp}°C calculation failed')

    # Test 4: TX Interval variation
    print('\n4. Testing TX interval variation...')
    intervals = [1, 5, 15, 30, 60]
    for interval in intervals:
        calc_response = requests.post('http://localhost:8001/calculate-battery-life', json={
            'temperature': 20.0,
            'tx_power_dbm': 14,
            'tx_interval_minutes': interval,
            'spreading_factor': 'SF7',
            'sleep_mode': 'stop2',
            'sensor_resolution_bits': 12,
            'measurements_per_tx': 1,
            'led_enabled': True,
            'battery_count': 2
        })
        
        if calc_response.status_code == 200:
            result = calc_response.json()
            tx_per_day = 24 * 60 / interval
            print(f'   {interval} min: {result["battery_life_years"]:.1f} years, {tx_per_day:.0f} TX/day')
        else:
            print(f'   ❌ {interval} min calculation failed')

    # Test 5: Battery count variation
    print('\n5. Testing battery count variation...')
    battery_counts = [1, 2, 3, 4]
    for count in battery_counts:
        calc_response = requests.post('http://localhost:8001/calculate-battery-life', json={
            'temperature': 20.0,
            'tx_power_dbm': 14,
            'tx_interval_minutes': 15,
            'spreading_factor': 'SF7',
            'sleep_mode': 'stop2',
            'sensor_resolution_bits': 12,
            'measurements_per_tx': 1,
            'led_enabled': True,
            'battery_count': count
        })
        
        if calc_response.status_code == 200:
            result = calc_response.json()
            voltage = result["battery_details"]["series_voltage"]
            capacity = result["battery_details"]["total_capacity_wh"]
            print(f'   {count} batteries: {result["battery_life_years"]:.1f} years, {voltage:.1f}V, {capacity:.1f}Wh')
        else:
            print(f'   ❌ {count} batteries calculation failed')

    print('\n🎉 New interface testing completed!')
    print('\n✅ SINGLE-PAGE "WHAT IF" INTERFACE READY!')
    print('   - Real-time parameter sliders')
    print('   - Immediate battery life feedback')
    print('   - No modal navigation required')
    print('   - Professional parameter controls')
    print('   - Comprehensive results display')
    
    return True

if __name__ == "__main__":
    success = test_new_interface()
    if success:
        print('\n🚀 Ready for firmware team testing!')
    else:
        print('\n❌ Interface needs fixes!')

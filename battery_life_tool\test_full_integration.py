#!/usr/bin/env python3
"""
Test full integration: Backend API + Frontend connectivity
"""

import requests
import json

def test_full_integration():
    print("=== FULL INTEGRATION TEST ===")
    
    # Test multiple scenarios that the frontend would send
    test_scenarios = [
        {
            "name": "Default Settings",
            "data": {
                "temperature": 20.0,
                "tx_power_dbm": 14,
                "tx_interval_minutes": 15,
                "spreading_factor": "SF7"
            }
        },
        {
            "name": "Freezer Application",
            "data": {
                "temperature": -20.0,
                "tx_power_dbm": 14,
                "tx_interval_minutes": 15,
                "spreading_factor": "SF7"
            }
        },
        {
            "name": "Extreme Cold",
            "data": {
                "temperature": -40.0,
                "tx_power_dbm": 20,
                "tx_interval_minutes": 10,
                "spreading_factor": "SF12"
            }
        },
        {
            "name": "Power Saving Mode",
            "data": {
                "temperature": 0.0,
                "tx_power_dbm": 7,
                "tx_interval_minutes": 60,
                "spreading_factor": "SF7"
            }
        }
    ]
    
    print(f"\nTesting backend API at: http://localhost:8001")
    
    all_passed = True
    
    for scenario in test_scenarios:
        try:
            print(f"\n--- {scenario['name']} ---")
            
            response = requests.post(
                'http://localhost:8001/calculate-battery-life',
                json=scenario['data'],
                timeout=10
            )
            
            if response.status_code == 200:
                result = response.json()
                
                print(f"✅ Battery Life: {result['battery_life_years']} years")
                print(f"   Daily Energy: {result['daily_energy_mah']:.6f} mAh")
                print(f"   Energy Split: LoRa {result['energy_breakdown']['lora_percent']}%, Sensor {result['energy_breakdown']['sensor_percent']}%")
                print(f"   Confidence: {result['confidence_description']} ({result['confidence_level']:.0%})")
                
                # Validate results are reasonable
                if 0.1 <= result['battery_life_years'] <= 500:
                    print(f"   ✅ Realistic battery life range")
                else:
                    print(f"   ❌ Unrealistic battery life: {result['battery_life_years']} years")
                    all_passed = False
                    
            else:
                print(f"❌ HTTP Error: {response.status_code}")
                print(f"   Response: {response.text}")
                all_passed = False
                
        except Exception as e:
            print(f"❌ Request Error: {e}")
            all_passed = False
    
    print(f"\n=== INTEGRATION TEST SUMMARY ===")
    if all_passed:
        print("✅ ALL TESTS PASSED")
        print("✅ Backend API working correctly")
        print("✅ All scenarios return realistic results")
        print("✅ Frontend can connect to backend")
        print("\n🎉 PHASE 2 INTEGRATION SUCCESSFUL!")
        print("\nServers running:")
        print("  Backend API: http://localhost:8001")
        print("  Frontend UI: http://localhost:3000")
        print("  API Docs: http://localhost:8001/docs")
    else:
        print("❌ SOME TESTS FAILED")
        print("❌ Integration issues detected")
    
    return all_passed

if __name__ == "__main__":
    test_full_integration()

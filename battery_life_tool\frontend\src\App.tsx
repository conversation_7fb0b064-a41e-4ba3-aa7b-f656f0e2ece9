import React, { useState, useEffect, useCallback } from 'react';
import './App.css';

interface BatteryLifeRequest {
  temperature: number;
  tx_power_dbm: number;
  tx_interval_minutes: number;
  spreading_factor: string;
  sleep_mode: string;
  sensor_resolution_bits: number;
  measurements_per_tx: number;
  led_enabled: boolean;
  battery_count: number;
}

interface BatteryLifeResponse {
  battery_life_days: number;
  battery_life_years: number;
  daily_energy_mah: number;
  energy_breakdown: {
    lora_percent: number;
    sensor_percent: number;
    led_percent: number;
  };
  battery_info: {
    effective_capacity_mah: number;
    average_current_ma: number;
  };
  lora_details: {
    tx_current_ma: number;
    sleep_current_ua: number;
    tx_time_ms: number;
    sleep_time_hours: number;
    tx_per_day: number;
  };
  sensor_details: {
    active_current_ma: number;
    standby_current_ua: number;
    conversion_time_ms: number;
    standby_time_hours: number;
    measurements_per_day: number;
  };
  confidence_level: number;
  confidence_description: string;
  input_parameters: BatteryLifeRequest;
}

interface BatteryLifeResult {
  battery_life_years: number;
  battery_life_months: number;
  total_energy_wh: number;
  average_current_ua: number;
  lora_details: {
    tx_current_ma: number;
    sleep_current_ua: number;
    tx_energy_per_cycle_mwh: number;
    sleep_energy_per_cycle_mwh: number;
    duty_cycle_percent: number;
  };
  sensor_details: {
    active_current_ma: number;
    standby_current_ua: number;
    energy_per_measurement_mwh: number;
    measurements_per_day: number;
  };
  battery_details: {
    total_capacity_wh: number;
    temperature_derating_factor: number;
    usable_capacity_wh: number;
    series_voltage: number;
  };
}

interface Parameters {
  temperature: number;
  tx_power_dbm: number;
  tx_interval_minutes: number;
  spreading_factor: string;
  sleep_mode: string;
  sensor_resolution_bits: number;
  measurements_per_tx: number;
  led_enabled: boolean;
  battery_count: number;
}

interface Configuration {
  lora_config: {
    tx_current_7dbm: number;
    tx_current_14dbm: number;
    tx_current_20dbm: number;
    sleep_current_stop2: number;
    sleep_current_stop1: number;
  };
  sensor_config: {
    active_current_ma: number;
    standby_current_ua: number;
  };
}

function App() {
  // Core parameters for "what if" analysis
  const [parameters, setParameters] = useState<Parameters>({
    temperature: 20.0,
    tx_power_dbm: 14,
    tx_interval_minutes: 15,
    spreading_factor: 'SF7',
    sleep_mode: 'stop2',
    sensor_resolution_bits: 12,
    measurements_per_tx: 1,
    led_enabled: true,
    battery_count: 2
  });

  // Results and loading states
  const [result, setResult] = useState<BatteryLifeResult | null>(null);
  const [loading, setLoading] = useState<boolean>(false);
  const [configuration, setConfiguration] = useState<Configuration | null>(null);
  const [configLoading, setConfigLoading] = useState<boolean>(true);

  // Load configuration defaults
  const loadConfiguration = async () => {
    try {
      setConfigLoading(true);
      const response = await fetch('http://localhost:8001/get-configuration');
      if (response.ok) {
        const data = await response.json();
        setConfiguration(data.configuration);
      }
    } catch (error) {
      console.error('Failed to load configuration:', error);
    } finally {
      setConfigLoading(false);
    }
  };

  // Real-time battery life calculation with debouncing
  const calculateBatteryLife = useCallback(async (params: Parameters) => {
    try {
      setLoading(true);
      const response = await fetch('http://localhost:8001/calculate-battery-life', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(params)
      });

      if (response.ok) {
        const data = await response.json();
        setResult(data);
      }
    } catch (error) {
      console.error('Calculation failed:', error);
    } finally {
      setLoading(false);
    }
  }, []);

  // Debounced calculation trigger
  useEffect(() => {
    const timeoutId = setTimeout(() => {
      calculateBatteryLife(parameters);
    }, 300); // 300ms debounce

    return () => clearTimeout(timeoutId);
  }, [parameters, calculateBatteryLife]);

  // Load configuration on mount
  useEffect(() => {
    loadConfiguration();
  }, []);

  // Parameter update handler
  const updateParameter = (key: keyof Parameters, value: any) => {
    setParameters(prev => ({ ...prev, [key]: value }));
  };

  // Get current TX current based on power level
  const getCurrentTxCurrent = (powerDbm: number): number => {
    if (!configuration) return 24.0; // fallback

    const { lora_config } = configuration;
    if (powerDbm <= 7) return lora_config.tx_current_7dbm;
    if (powerDbm <= 14) return lora_config.tx_current_14dbm;
    return lora_config.tx_current_20dbm;
  };

  // Get L91 capacity derating factor
  const getCapacityDerating = (temp: number): number => {
    if (temp >= 20) return 1.0;
    if (temp >= 0) return 0.85;
    if (temp >= -20) return 0.65;
    return 0.33; // -40°C
  };

  if (configLoading) {
    return (
      <div className="app">
        <div className="loading-screen">
          <h2>🔋 Loading COLDRA Battery Life Assessment Tool...</h2>
          <p>Loading configuration defaults...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="app">
      <header className="app-header">
        <h1>🔋 COLDRA Battery Life Assessment Tool</h1>
        <p>Real-time "What If" Analysis for LoRaWAN Temperature Sensor</p>
      </header>

      <div className="main-content">
        {/* LEFT PANEL: Parameter Controls */}
        <div className="parameters-panel">
          <h2>📊 Parameter Controls</h2>

          {/* LoRa Module Section */}
          <div className="parameter-section">
            <h3>📡 LoRa Module (RAK3172)</h3>

            <div className="parameter-control">
              <label>TX Power: {parameters.tx_power_dbm} dBm</label>
              <input
                type="range"
                min="7"
                max="20"
                step="1"
                value={parameters.tx_power_dbm}
                onChange={(e) => updateParameter('tx_power_dbm', parseInt(e.target.value))}
                className="slider"
              />
              <div className="parameter-info">
                Current: {getCurrentTxCurrent(parameters.tx_power_dbm).toFixed(1)} mA
              </div>
            </div>

            <div className="parameter-control">
              <label>TX Interval: {parameters.tx_interval_minutes} minutes</label>
              <input
                type="range"
                min="1"
                max="60"
                step="1"
                value={parameters.tx_interval_minutes}
                onChange={(e) => updateParameter('tx_interval_minutes', parseInt(e.target.value))}
                className="slider"
              />
              <div className="parameter-info">
                {(24 * 60 / parameters.tx_interval_minutes).toFixed(1)} transmissions/day
              </div>
            </div>

            <div className="parameter-control">
              <label>Spreading Factor:</label>
              <select
                value={parameters.spreading_factor}
                onChange={(e) => updateParameter('spreading_factor', e.target.value)}
                className="dropdown"
              >
                <option value="SF7">SF7 (Fastest, Shortest Range)</option>
                <option value="SF8">SF8</option>
                <option value="SF9">SF9</option>
                <option value="SF10">SF10</option>
                <option value="SF11">SF11</option>
                <option value="SF12">SF12 (Slowest, Longest Range)</option>
              </select>
            </div>
          </div>

          {/* L91 Battery Section */}
          <div className="parameter-section">
            <h3>🔋 L91 Lithium Battery</h3>

            <div className="parameter-control">
              <label>Temperature: {parameters.temperature}°C</label>
              <input
                type="range"
                min="-40"
                max="40"
                step="5"
                value={parameters.temperature}
                onChange={(e) => updateParameter('temperature', parseInt(e.target.value))}
                className="slider temperature-slider"
              />
              <div className="parameter-info">
                Capacity: {(getCapacityDerating(parameters.temperature) * 100).toFixed(0)}% of rated
              </div>
            </div>

            <div className="parameter-control">
              <label>Battery Count: {parameters.battery_count}</label>
              <input
                type="range"
                min="1"
                max="4"
                step="1"
                value={parameters.battery_count}
                onChange={(e) => updateParameter('battery_count', parseInt(e.target.value))}
                className="slider"
              />
              <div className="parameter-info">
                {parameters.battery_count === 1 ? '1.5V' :
                 parameters.battery_count === 2 ? '3.0V (Series)' :
                 parameters.battery_count === 3 ? '4.5V (Series)' : '6.0V (Series)'}
              </div>
            </div>
          </div>

          {/* Temperature Sensor Section */}
          <div className="parameter-section">
            <h3>🌡️ MAX31820 Temperature Sensor</h3>

            <div className="parameter-control">
              <label>Resolution: {parameters.sensor_resolution_bits} bits</label>
              <input
                type="range"
                min="9"
                max="12"
                step="1"
                value={parameters.sensor_resolution_bits}
                onChange={(e) => updateParameter('sensor_resolution_bits', parseInt(e.target.value))}
                className="slider"
              />
              <div className="parameter-info">
                Conversion time: {parameters.sensor_resolution_bits === 12 ? '750ms' :
                                 parameters.sensor_resolution_bits === 11 ? '375ms' :
                                 parameters.sensor_resolution_bits === 10 ? '188ms' : '94ms'}
              </div>
            </div>
          </div>
        </div>

        {/* RIGHT PANEL: Real-time Results */}
        <div className="results-panel">
          <h2>⚡ Real-time Results</h2>

          {loading ? (
            <div className="loading-indicator">
              <p>🔄 Calculating...</p>
            </div>
          ) : result ? (
            <>
              {/* Main Battery Life Display */}
              <div className="battery-life-display">
                <div className="battery-life-main">
                  <span className="battery-life-number">{result.battery_life_years.toFixed(1)}</span>
                  <span className="battery-life-unit">YEARS</span>
                </div>
                <div className="battery-life-sub">
                  ({result.battery_life_months.toFixed(0)} months)
                </div>
              </div>

              {/* Current Breakdown */}
              <div className="current-breakdown">
                <h3>Current Consumption Breakdown</h3>
                <div className="breakdown-item">
                  <span>LoRa TX Current:</span>
                  <span>{result.lora_details.tx_current_ma.toFixed(1)} mA</span>
                </div>
                <div className="breakdown-item">
                  <span>LoRa Sleep Current:</span>
                  <span>{result.lora_details.sleep_current_ua.toFixed(1)} µA</span>
                </div>
                <div className="breakdown-item">
                  <span>Sensor Active Current:</span>
                  <span>{result.sensor_details.active_current_ma.toFixed(1)} mA</span>
                </div>
                <div className="breakdown-item">
                  <span>Average Current:</span>
                  <span>{result.average_current_ua.toFixed(1)} µA</span>
                </div>
              </div>

              {/* Battery Details */}
              <div className="battery-details">
                <h3>Battery Analysis</h3>
                <div className="breakdown-item">
                  <span>Total Capacity:</span>
                  <span>{result.battery_details.total_capacity_wh.toFixed(1)} Wh</span>
                </div>
                <div className="breakdown-item">
                  <span>Temperature Derating:</span>
                  <span>{(result.battery_details.temperature_derating_factor * 100).toFixed(0)}%</span>
                </div>
                <div className="breakdown-item">
                  <span>Usable Capacity:</span>
                  <span>{result.battery_details.usable_capacity_wh.toFixed(1)} Wh</span>
                </div>
                <div className="breakdown-item">
                  <span>System Voltage:</span>
                  <span>{result.battery_details.series_voltage.toFixed(1)} V</span>
                </div>
              </div>
            </>
          ) : (
            <div className="no-results">
              <p>Adjust parameters to see battery life analysis</p>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};



export default App;

import React, { useState, useEffect } from 'react';
import axios from 'axios';

const Statistics = () => {
  const [dailyStats, setDailyStats] = useState(null);
  const [selectedDate, setSelectedDate] = useState(new Date().toISOString().split('T')[0]);
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    fetchDailyStats();
  }, [selectedDate]);

  const fetchDailyStats = async () => {
    setLoading(true);
    try {
      const response = await axios.get(`http://localhost:3000/api/statistics/daily?date=${selectedDate}`);
      setDailyStats(response.data);
    } catch (error) {
      console.error('Failed to fetch daily statistics:', error);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="statistics">
      <div className="page-header">
        <h1>📈 Production Statistics</h1>
        <p>Daily production metrics, yield analysis, and operator performance</p>
      </div>

      <div className="stats-controls">
        <div className="card">
          <div className="card-title">📅 Date Selection</div>
          <div className="date-selector">
            <div className="form-group">
              <label className="form-label">Select Date</label>
              <input
                type="date"
                className="form-input"
                value={selectedDate}
                onChange={(e) => setSelectedDate(e.target.value)}
              />
            </div>
            <button className="btn btn-primary" onClick={fetchDailyStats}>
              Refresh Statistics
            </button>
          </div>
        </div>
      </div>

      <div className="stats-grid">
        <div className="card">
          <div className="card-title">📊 Daily Production Summary</div>
          {loading ? (
            <div className="loading">Loading statistics...</div>
          ) : dailyStats ? (
            <div className="stats-summary">
              <div className="summary-item">
                <div className="summary-label">Date:</div>
                <div className="summary-value">{dailyStats.date}</div>
              </div>
              <div className="summary-item">
                <div className="summary-label">Total Devices:</div>
                <div className="summary-value">{dailyStats.statistics.total_devices || 0}</div>
              </div>
              <div className="summary-item">
                <div className="summary-label">Programmed:</div>
                <div className="summary-value status-info">{dailyStats.statistics.programmed || 0}</div>
              </div>
              <div className="summary-item">
                <div className="summary-label">Provisioned:</div>
                <div className="summary-value status-info">{dailyStats.statistics.provisioned || 0}</div>
              </div>
              <div className="summary-item">
                <div className="summary-label">Tested (Pass):</div>
                <div className="summary-value status-success">{dailyStats.statistics.tested_pass || 0}</div>
              </div>
              <div className="summary-item">
                <div className="summary-label">Tested (Fail):</div>
                <div className="summary-value status-error">{dailyStats.statistics.tested_fail || 0}</div>
              </div>
            </div>
          ) : (
            <div className="no-data">No statistics available for selected date</div>
          )}
        </div>

        <div className="card">
          <div className="card-title">🎯 Yield Analysis</div>
          <div className="yield-metrics">
            <div className="metric-item">
              <div className="metric-value">0%</div>
              <div className="metric-label">Overall Yield</div>
            </div>
            <div className="metric-item">
              <div className="metric-value">0</div>
              <div className="metric-label">Devices/Hour</div>
            </div>
            <div className="metric-item">
              <div className="metric-value">0</div>
              <div className="metric-label">Target: 60-90</div>
            </div>
          </div>
        </div>

        <div className="card">
          <div className="card-title">👥 Operator Performance</div>
          <div className="operator-stats">
            <div className="operator-item">
              <div className="operator-name">OP001</div>
              <div className="operator-metrics">
                <span className="metric">0 devices</span>
                <span className="metric">0% yield</span>
              </div>
            </div>
            <div className="no-data">No operator data available</div>
          </div>
        </div>
      </div>

      <div className="card">
        <div className="card-title">📊 Statistics Endpoints (3 endpoints)</div>
        <div className="endpoints-list">
          <div className="endpoint-item">
            <span className="endpoint-number">21.</span>
            <span className="endpoint-name">GET /api/statistics/daily</span>
            <span className="endpoint-desc">Get daily production statistics</span>
          </div>
          <div className="endpoint-item">
            <span className="endpoint-number">22.</span>
            <span className="endpoint-name">GET /api/statistics/operator</span>
            <span className="endpoint-desc">Get operator performance statistics</span>
          </div>
          <div className="endpoint-item">
            <span className="endpoint-number">23.</span>
            <span className="endpoint-name">GET /api/statistics/yield</span>
            <span className="endpoint-desc">Get production yield statistics</span>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Statistics;

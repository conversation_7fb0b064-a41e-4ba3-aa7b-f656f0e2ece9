# MUST RULE: Development Approach

## Core Principle
**ALWAYS BREAK DOWN BIG TASKS INTO SMALL, <PERSON><PERSON><PERSON><PERSON>LL<PERSON> COMPLETE STEPS THAT CAN BE VERIFIED BY HUMAN REVIEW**

## Implementation Guidelines

### 1. Task Decomposition
- Before starting any significant implementation, decompose it into smaller sub-tasks
- Each sub-task should represent a logically complete unit of work
- Sub-tasks should be small enough to be easily understood and verified

### 2. Verification Points
- After completing each sub-task, ensure it can be independently verified
- Provide clear explanations of what was implemented and why
- Include any relevant test results or verification steps

### 3. Checkpoint Communication
- Clearly indicate when you've completed a verifiable unit of work
- Ask for feedback before proceeding to the next sub-task
- Do not skip ahead to larger implementations without verification

### 4. Documentation
- Document each completed sub-task with sufficient detail for review
- Explain the reasoning behind implementation decisions
- Highlight any assumptions or design choices made

## Example Approach

### Instead of:
"Implement the entire database manager"

### Do:
1. Create database schema design
2. Implement database connection functionality
3. Implement basic CRUD operations for devices table
4. Add transaction support
5. Implement error handling
6. Add connection pooling
7. etc.

And after each step, request verification before proceeding.

## Enforcement
This rule must be followed at all times during development. No large implementations should be done without breaking them down into smaller, verifiable steps first.

**Reviewed and Approved:** [To be filled]
**Date:** [To be filled]
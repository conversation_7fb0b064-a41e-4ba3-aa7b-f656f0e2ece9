battery-life-tool/
├── backend/
│   ├── main.py
│   └── requirements.txt
└── frontend/
    ├── public/
    │   └── index.html
    ├── src/
    │   ├── App.js
    │   ├── App.css
    │   ├── index.js
    │   └── index.css
    └── package.json
	
	2. Backend Files
backend/requirements.txt

fastapi
uvicorn
pydantic
numpy
scipy

backend/main.py

from fastapi import FastAPI
from pydantic import BaseModel
from typing import Dict, Any
import numpy as np
from scipy.interpolate import interp1d
from fastapi.middleware.cors import CORSMiddleware

app = FastAPI()

# Add CORS middleware to allow requests from the frontend
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Battery Model for L91
class BatteryModel:
    def __init__(self):
        # Temperature vs. capacity (mAh) from datasheet
        self.temp_points = np.array([-40, -20, 0, 20, 40, 60])
        self.capacity_points = np.array([1800, 2400, 2900, 3000, 2950, 2850])
        self.capacity_func = interp1d(self.temp_points, self.capacity_points, kind='linear', bounds_error=False, fill_value="extrapolate")
        
        # Load vs. capacity (mAh) from datasheet
        self.load_points = np.array([1, 10, 25, 50, 100, 250, 1000])
        self.load_capacity_points = np.array([3000, 2950, 2850, 2700, 2400, 1800, 800])
        self.load_capacity_func = interp1d(self.load_points, self.load_capacity_points, kind='linear', bounds_error=False, fill_value="extrapolate")
    
    def get_capacity_at_temp(self, temp):
        return float(self.capacity_func(temp))
    
    def get_effective_capacity(self, temp, avg_current):
        # Get base capacity at temperature
        base_capacity = self.get_capacity_at_temp(temp)
        # Adjust for load (using average current in mA)
        load_factor = float(self.load_capacity_func(avg_current)) / 3000.0  # Normalize to nominal capacity
        effective_capacity = base_capacity * load_factor
        return effective_capacity

# RAK3172 Model
class RAK3172Model:
    def __init__(self):
        # TX current (mA) vs. power level (dBm) at 25°C
        self.tx_current_data = {
            7: 15,
            10: 17,
            14: 24,
            17: 44,
            20: 87,
            22: 120  # estimated
        }
        
        # Sleep current (µA) vs. mode at 25°C
        self.sleep_current_data = {
            "light": 5.0,    # Light sleep (estimated)
            "deep": 1.69,   # Deep sleep (Stop2) from datasheet
            "ultra": 0.031  # Ultra deep (Shutdown) from datasheet
        }
    
    def get_tx_current(self, power_dbm):
        return self.tx_current_data.get(power_dbm, 87)  # default to 20dBm if not found
    
    def get_sleep_current(self, mode):
        return self.sleep_current_data.get(mode, 1.69)  # default to deep sleep

# Battery Life Calculator
class BatteryLifeCalculator:
    def __init__(self):
        self.battery = BatteryModel()
        self.rak3172 = RAK3172Model()
    
    def calculate(self, params: Dict[str, Any]):
        # Extract parameters
        temperature = params.get("temperature", 20)
        tx_power = params.get("tx_power", 14)
        tx_interval = params.get("tx_interval", 15)  # in minutes
        tx_duration = params.get("tx_duration", 0.5)  # in seconds
        sleep_mode = params.get("sleep_mode", "deep")
        
        # Calculate daily transmissions
        transmissions_per_day = (24 * 60) / tx_interval
        
        # Calculate TX energy per day (in mAh)
        tx_current = self.rak3172.get_tx_current(tx_power)  # mA
        tx_energy_per_transmission = tx_current * (tx_duration / 3600)  # mAh (since 1 hour = 3600 seconds)
        tx_energy_per_day = tx_energy_per_transmission * transmissions_per_day
        
        # Calculate sleep energy per day (in mAh)
        sleep_current_ua = self.rak3172.get_sleep_current(sleep_mode)  # µA
        sleep_current_ma = sleep_current_ua / 1000.0  # mA
        sleep_energy_per_day = sleep_current_ma * 24  # mAh (24 hours)
        
        # Total energy per day
        total_energy_per_day = tx_energy_per_day + sleep_energy_per_day
        
        # Calculate effective battery capacity
        # Average current over a day (for load adjustment)
        avg_current = total_energy_per_day / 24  # mA
        effective_capacity = self.battery.get_effective_capacity(temperature, avg_current)  # mAh
        
        # Calculate battery life in days
        battery_life_days = effective_capacity / total_energy_per_day
        battery_life_years = battery_life_days / 365.25
        
        # Calculate power breakdown percentages
        tx_power_percent = (tx_energy_per_day / total_energy_per_day) * 100
        sleep_power_percent = (sleep_energy_per_day / total_energy_per_day) * 100
        
        # Capacity reduction due to temperature and load
        nominal_capacity = self.battery.get_capacity_at_temp(20)  # at 20°C and nominal load
        capacity_reduction_percent = ((nominal_capacity - effective_capacity) / nominal_capacity) * 100
        
        return {
            "battery_life_days": battery_life_days,
            "battery_life_years": battery_life_years,
            "tx_power_percent": tx_power_percent,
            "sleep_power_percent": sleep_power_percent,
            "nominal_capacity": nominal_capacity,
            "effective_capacity": effective_capacity,
            "capacity_reduction_percent": capacity_reduction_percent
        }

# Pydantic model for request body
class BatteryParams(BaseModel):
    temperature: float
    tx_power: int
    tx_interval: int
    tx_duration: float
    sleep_mode: str

@app.post("/calculate")
async def calculate_battery_life(params: BatteryParams):
    calculator = BatteryLifeCalculator()
    result = calculator.calculate(params.dict())
    return result
	
3. Frontend Files
frontend/package.json

{
  "name": "battery-life-frontend",
  "version": "0.1.0",
  "private": true,
  "dependencies": {
    "@testing-library/jest-dom": "^5.16.5",
    "@testing-library/react": "^13.4.0",
    "@testing-library/user-event": "^13.5.0",
    "axios": "^1.4.0",
    "react": "^18.2.0",
    "react-dom": "^18.2.0",
    "react-scripts": "5.0.1",
    "web-vitals": "^2.1.4"
  },
  "scripts": {
    "start": "react-scripts start",
    "build": "react-scripts build",
    "test": "react-scripts test",
    "eject": "react-scripts eject"
  },
  "eslintConfig": {
    "extends": [
      "react-app",
      "react-app/jest"
    ]
  },
  "browserslist": {
    "production": [
      ">0.2%",
      "not dead",
      "not op_mini all"
    ],
    "development": [
      "last 1 chrome version",
      "last 1 firefox version",
      "last 1 safari version"
    ]
  }
}

frontend/public/index.html

<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="utf-8" />
    <link rel="icon" href="%PUBLIC_URL%/favicon.ico" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <meta name="theme-color" content="#000000" />
    <meta
      name="description"
      content="Battery Life Assessment Tool"
    />
    <title>Battery Life Assessment Tool</title>
  </head>
  <body>
    <noscript>You need to enable JavaScript to run this app.</noscript>
    <div id="root"></div>
  </body>
</html>

frontend/src/index.js

import React from 'react';
import ReactDOM from 'react-dom/client';
import './index.css';
import App from './App';

const root = ReactDOM.createRoot(document.getElementById('root'));
root.render(
  <React.StrictMode>
    <App />
  </React.StrictMode>
);

frontend/src/index.css

body {
  margin: 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
    'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

code {
  font-family: source-code-pro, Menlo, Monaco, Consolas, 'Courier New',
    monospace;
}

frontend/src/App.js

import React, { useState, useEffect } from 'react';
import axios from 'axios';
import './App.css';

function App() {
  const [params, setParams] = useState({
    temperature: -20,
    tx_power: 14,
    tx_interval: 15,
    tx_duration: 0.5,
    sleep_mode: 'deep'
  });
  
  const [result, setResult] = useState(null);
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    const calculateBatteryLife = async () => {
      setLoading(true);
      try {
        const response = await axios.post('http://localhost:8000/calculate', params);
        setResult(response.data);
      } catch (error) {
        console.error('Error calculating battery life:', error);
      }
      setLoading(false);
    };

    calculateBatteryLife();
  }, [params]);

  const handleParamChange = (key, value) => {
    setParams(prev => ({ ...prev, [key]: value }));
  };

  return (
    <div className="App">
      <h1>LoRa Sensor Battery Life Calculator</h1>
      
      <div className="params-panel">
        <h2>Parameters</h2>
        
        <div className="param-control">
          <label>Temperature: {params.temperature}°C</label>
          <input 
            type="range" 
            min="-40" 
            max="60" 
            value={params.temperature}
            onChange={e => handleParamChange('temperature', parseFloat(e.target.value))}
          />
        </div>
        
        <div className="param-control">
          <label>TX Power (dBm)</label>
          <select 
            value={params.tx_power}
            onChange={e => handleParamChange('tx_power', parseInt(e.target.value))}
          >
            <option value="7">7 dBm (15mA)</option>
            <option value="10">10 dBm (17mA)</option>
            <option value="14">14 dBm (24mA)</option>
            <option value="17">17 dBm (44mA)</option>
            <option value="20">20 dBm (87mA)</option>
            <option value="22">22 dBm (120mA)</option>
          </select>
        </div>
        
        <div className="param-control">
          <label>TX Interval (minutes)</label>
          <input 
            type="number" 
            min="1" 
            max="1440" 
            value={params.tx_interval}
            onChange={e => handleParamChange('tx_interval', parseInt(e.target.value))}
          />
        </div>
        
        <div className="param-control">
          <label>TX Duration (seconds)</label>
          <input 
            type="number" 
            step="0.1" 
            min="0.1" 
            max="10" 
            value={params.tx_duration}
            onChange={e => handleParamChange('tx_duration', parseFloat(e.target.value))}
          />
        </div>
        
        <div className="param-control">
          <label>Sleep Mode</label>
          <select 
            value={params.sleep_mode}
            onChange={e => handleParamChange('sleep_mode', e.target.value)}
          >
            <option value="light">Light Sleep (5µA)</option>
            <option value="deep">Deep Sleep (1.69µA)</option>
            <option value="ultra">Ultra Deep (0.031µA)</option>
          </select>
        </div>
      </div>
      
      <div className="results-panel">
        <h2>Battery Life Prediction</h2>
        {loading ? (
          <p>Calculating...</p>
        ) : result ? (
          <>
            <div className="battery-life">
              <div className="life-value">{result.battery_life_years.toFixed(1)} years</div>
              <div className="life-detail">({result.battery_life_days.toFixed(0)} days)</div>
            </div>
            
            <div className="power-breakdown">
              <h3>Power Consumption Breakdown</h3>
              <div className="breakdown-item">
                <span>TX Power:</span>
                <span>{result.tx_power_percent.toFixed(1)}%</span>
              </div>
              <div className="breakdown-item">
                <span>Sleep Power:</span>
                <span>{result.sleep_power_percent.toFixed(1)}%</span>
              </div>
            </div>
            
            <div className="capacity-info">
              <h3>Battery Capacity</h3>
              <div className="capacity-item">
                <span>Nominal Capacity:</span>
                <span>{result.nominal_capacity.toFixed(0)} mAh</span>
              </div>
              <div className="capacity-item">
                <span>Effective Capacity:</span>
                <span>{result.effective_capacity.toFixed(0)} mAh</span>
              </div>
              <div className="capacity-item">
                <span>Capacity Reduction:</span>
                <span>{result.capacity_reduction_percent.toFixed(1)}%</span>
              </div>
            </div>
          </>
        ) : (
          <p>No result</p>
        )}
      </div>
    </div>
  );
}

export default App;

frontend/src/App.css

.App {
  font-family: Arial, sans-serif;
  max-width: 800px;
  margin: 0 auto;
  padding: 20px;
}

h1 {
  text-align: center;
}

.params-panel, .results-panel {
  background-color: #f5f5f5;
  padding: 20px;
  border-radius: 8px;
  margin-bottom: 20px;
}

.param-control {
  margin-bottom: 15px;
}

.param-control label {
  display: block;
  margin-bottom: 5px;
  font-weight: bold;
}

.param-control input, .param-control select {
  width: 100%;
  padding: 8px;
  box-sizing: border-box;
}

.battery-life {
  text-align: center;
  margin: 20px 0;
}

.life-value {
  font-size: 48px;
  font-weight: bold;
  color: #2e7d32;
}

.life-detail {
  font-size: 18px;
  color: #666;
}

.power-breakdown, .capacity-info {
  margin-top: 20px;
}

.power-breakdown h3, .capacity-info h3 {
  margin-bottom: 10px;
}

.breakdown-item, .capacity-item {
  display: flex;
  justify-content: space-between;
  padding: 5px 0;
  border-bottom: 1px solid #ddd;
}

.breakdown-item:last-child, .capacity-item:last-child {
  border-bottom: none;
}

4. README.md (Create this in the root directory)

# Battery Life Assessment Tool MVP

This is a minimal viable product (MVP) for a battery life assessment tool for LoRa sensors.

## Project Structure

battery-life-tool/
├── backend/
│ ├── main.py # FastAPI application
│ └── requirements.txt # Python dependencies
└── frontend/
├── public/
├── src/
│ ├── App.js # Main React component
│ ├── App.css # Styles for App.js
│ ├── index.js # Entry point for React
│ └── index.css # Global styles
└── package.json # Node.js dependencies


## Prerequisites

- Python 3.8+
- Node.js 14+
- npm or yarn

## Setup Instructions

### Backend Setup

1. Navigate to the backend directory:

cd backend


2. Create a virtual environment:


3. Activate the virtual environment:
- On Windows:
  ```
  venv\Scripts\activate
  ```
- On macOS/Linux:
  ```
  source venv/bin/activate
  ```

4. Install the required packages:

pip install -r requirements.txt


5. Run the backend server:

The backend will be available at `http://localhost:8000`.

### Frontend Setup

1. Navigate to the frontend directory:

uvicorn main:app --reload

The backend will be available at `http://localhost:8000`.

### Frontend Setup

1. Navigate to the frontend directory:


2. Install the required packages:

npm install


3. Start the frontend development server:

npm start

The frontend will be available at `http://localhost:3000`.

## Usage

1. Make sure both the backend and frontend are running.
2. Open your browser and go to `http://localhost:3000`.
3. Adjust the parameters using the sliders and dropdowns.
4. The battery life prediction will update automatically.

## Parameters

- **Temperature**: Operating temperature of the device in °C.
- **TX Power**: Transmission power level in dBm.
- **TX Interval**: How often the device transmits data in minutes.
- **TX Duration**: Duration of each transmission in seconds.
- **Sleep Mode**: Power saving mode during inactivity.

## How It Works

The tool calculates the battery life based on:
1. The L91 battery's capacity at the given temperature and load.
2. The RAK3172 module's power consumption during transmission and sleep.
3. The user-configurable parameters.

The calculation is performed by the backend (FastAPI) and the results are displayed by the frontend (React).

The frontend will be available at `http://localhost:3000`.

## Usage

1. Make sure both the backend and frontend are running.
2. Open your browser and go to `http://localhost:3000`.
3. Adjust the parameters using the sliders and dropdowns.
4. The battery life prediction will update automatically.

## Parameters

- **Temperature**: Operating temperature of the device in °C.
- **TX Power**: Transmission power level in dBm.
- **TX Interval**: How often the device transmits data in minutes.
- **TX Duration**: Duration of each transmission in seconds.
- **Sleep Mode**: Power saving mode during inactivity.

## How It Works

The tool calculates the battery life based on:
1. The L91 battery's capacity at the given temperature and load.
2. The RAK3172 module's power consumption during transmission and sleep.
3. The user-configurable parameters.

The calculation is performed by the backend (FastAPI) and the results are displayed by the frontend (React).

How to Create and Run the Project
Step 1: Create the Project Files
Create a folder named battery-life-tool on your Desktop
Inside it, create two subfolders: backend and frontend
Create all the files as described above in their respective folders
Step 2: Create the Zip File
Right-click on the battery-life-tool folder
Select "Send to" > "Compressed (zipped) folder"
Name the zip file battery-life-tool.zip
Step 3: Extract and Run
Move the zip file to where you want to work with the project
Right-click the zip file and select "Extract All..."
Follow the instructions in the README.md file to run the application
Step 4: Run the Application
Open two Command Prompt windows
In the first window:

Line Wrapping

Collapse
Copy
1
2
3
4
5
cd C:\path\to\battery-life-tool\backend
python -m venv venv
venv\Scripts\activate
pip install -r requirements.txt
uvicorn main:app --reload
In the second window:

Line Wrapping

Collapse
Copy
1
2
3
cd C:\path\to\battery-life-tool\frontend
npm install
npm start
Open your browser and go to http://localhost:3000
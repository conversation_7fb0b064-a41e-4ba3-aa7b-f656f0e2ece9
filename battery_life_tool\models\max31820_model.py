"""
MAX31820 Temperature Sensor Model
=================================

This module contains the power consumption model for MAX31820 1-Wire temperature sensor
based on datasheet specifications.

Data source: MAX31820 Datasheet (Maxim Integrated)
- Standby current: 750-1000 nA
- Active current: 1-1.5 mA during conversion
- Conversion times: 93.75ms (9-bit) to 750ms (12-bit)
"""

import logging
from typing import Dict, Tuple

logger = logging.getLogger(__name__)


class MAX31820SensorModel:
    """
    Power consumption model for MAX31820 1-Wire temperature sensor.
    
    Based on datasheet specifications with temperature effects.
    """
    
    def __init__(self, config=None):
        """Initialize the MAX31820 sensor model with configurable parameters."""

        # Use configuration if provided, otherwise use datasheet defaults
        if config and hasattr(config, 'sensor_config'):
            sensor_config = config.sensor_config

            # Power consumption data (from FIRMWARE MEASUREMENTS)
            self.standby_current_na = sensor_config.standby_current_ua * 1000  # Convert µA to nA
            self.active_current_ma = sensor_config.active_current_ma

            # Conversion times by resolution (from FIRMWARE MEASUREMENTS)
            self.conversion_times_ms = {
                9:  sensor_config.conversion_time_9bit_ms,
                10: sensor_config.conversion_time_10bit_ms,
                11: sensor_config.conversion_time_11bit_ms,
                12: sensor_config.conversion_time_12bit_ms
            }

            # Temperature coefficients (from HARDWARE MEASUREMENTS)
            self.temp_coefficient = sensor_config.temp_coefficient

        else:
            # DATASHEET DEFAULTS - SHOULD BE REPLACED WITH MEASURED VALUES
            self.standby_current_na = 1000  # nA - MEASURE WITH REAL FIRMWARE
            self.active_current_ma = 1.5    # mA - MEASURE WITH REAL FIRMWARE

            # Conversion times by resolution - MEASURE WITH REAL FIRMWARE
            self.conversion_times_ms = {
                9:  93.75,   # ms - MEASURE WITH REAL FIRMWARE
                10: 187.5,   # ms - MEASURE WITH REAL FIRMWARE
                11: 375.0,   # ms - MEASURE WITH REAL FIRMWARE
                12: 750.0    # ms - MEASURE WITH REAL FIRMWARE
            }

            # Temperature coefficients - MEASURE WITH REAL HARDWARE
            self.temp_coefficient = 0.005  # 0.5% per °C - MEASURE WITH REAL HARDWARE

        # Operating parameters (usually from datasheet)
        self.operating_temp_range = (-55, 125)  # Celsius
        self.accuracy_range_high = (10, 45)     # ±0.5°C accuracy range
        self.supply_voltage_range = (3.0, 3.7)  # Volts (parasite power)
        
        logger.info("MAX31820 Sensor Model initialized")
    
    def get_standby_current_ua(self, temperature: float = 25.0) -> float:
        """
        Get standby current consumption at specific temperature.
        
        Args:
            temperature: Temperature in Celsius
            
        Returns:
            Standby current in µA
        """
        base_current_ua = self.standby_current_na / 1000  # Convert nA to µA
        
        # Apply temperature coefficient
        temp_factor = 1 + self.temp_coefficient * (temperature - 25.0)
        current = base_current_ua * temp_factor
        
        return max(current, 0.0)
    
    def get_active_current_ma(self, temperature: float = 25.0) -> float:
        """
        Get active current consumption during temperature conversion.
        
        Args:
            temperature: Temperature in Celsius
            
        Returns:
            Active current in mA
        """
        # Apply temperature coefficient
        temp_factor = 1 + self.temp_coefficient * (temperature - 25.0)
        current = self.active_current_ma * temp_factor
        
        return max(current, 0.0)
    
    def get_conversion_time_ms(self, resolution_bits: int = 12) -> float:
        """
        Get conversion time for specific resolution.
        
        Args:
            resolution_bits: Resolution in bits (9, 10, 11, or 12)
            
        Returns:
            Conversion time in milliseconds
        """
        if resolution_bits not in self.conversion_times_ms:
            logger.warning(f"Invalid resolution: {resolution_bits}, using 12-bit")
            resolution_bits = 12
        
        return self.conversion_times_ms[resolution_bits]
    
    def calculate_measurement_energy_mah(self, 
                                       resolution_bits: int = 12,
                                       temperature: float = 25.0) -> Tuple[float, Dict[str, float]]:
        """
        Calculate energy consumption for a single temperature measurement.
        
        Args:
            resolution_bits: Measurement resolution (9, 10, 11, or 12 bits)
            temperature: Operating temperature in Celsius
            
        Returns:
            Tuple of (energy_mAh, breakdown_dict)
        """
        breakdown = {}
        
        # Get conversion parameters
        active_current_ma = self.get_active_current_ma(temperature)
        conversion_time_ms = self.get_conversion_time_ms(resolution_bits)
        conversion_time_hours = conversion_time_ms / (1000 * 3600)
        
        breakdown['active_current_mA'] = active_current_ma
        breakdown['conversion_time_ms'] = conversion_time_ms
        breakdown['resolution_bits'] = resolution_bits
        breakdown['temperature_C'] = temperature
        
        # Calculate energy for one measurement
        energy_mah = active_current_ma * conversion_time_hours
        breakdown['energy_per_measurement_mAh'] = energy_mah
        
        return energy_mah, breakdown
    
    def calculate_daily_energy_mah(self,
                                 measurements_per_day: int,
                                 resolution_bits: int = 12,
                                 temperature: float = 25.0) -> Tuple[float, Dict[str, float]]:
        """
        Calculate total daily energy consumption for sensor.
        
        Args:
            measurements_per_day: Number of temperature measurements per day
            resolution_bits: Measurement resolution (9, 10, 11, or 12 bits)
            temperature: Operating temperature in Celsius
            
        Returns:
            Tuple of (daily_energy_mAh, detailed_breakdown)
        """
        breakdown = {}
        
        # Energy per measurement
        energy_per_measurement, measurement_breakdown = self.calculate_measurement_energy_mah(
            resolution_bits, temperature
        )
        breakdown.update(measurement_breakdown)
        breakdown['measurements_per_day'] = measurements_per_day
        
        # Daily measurement energy
        daily_measurement_energy = energy_per_measurement * measurements_per_day
        breakdown['daily_measurement_energy_mAh'] = daily_measurement_energy
        
        # Standby energy (time between measurements)
        standby_current_ua = self.get_standby_current_ua(temperature)
        standby_current_ma = standby_current_ua / 1000  # Convert µA to mA
        
        # Calculate standby time
        total_measurement_time_hours = (breakdown['conversion_time_ms'] * measurements_per_day) / (1000 * 3600)
        standby_time_hours = 24 - total_measurement_time_hours
        
        daily_standby_energy = standby_current_ma * standby_time_hours
        breakdown['standby_current_uA'] = standby_current_ua
        breakdown['standby_time_hours'] = standby_time_hours
        breakdown['daily_standby_energy_mAh'] = daily_standby_energy
        
        # Total daily energy
        total_daily_energy = daily_measurement_energy + daily_standby_energy
        breakdown['total_daily_energy_mAh'] = total_daily_energy
        
        # Energy breakdown percentages
        if total_daily_energy > 0:
            breakdown['measurement_energy_percent'] = (daily_measurement_energy / total_daily_energy) * 100
            breakdown['standby_energy_percent'] = (daily_standby_energy / total_daily_energy) * 100
        
        return total_daily_energy, breakdown


def test_max31820_model():
    """Test the MAX31820 sensor model with realistic scenarios."""
    sensor = MAX31820SensorModel()
    
    print("=== MAX31820 Sensor Model Test ===")
    
    # Test 1: Typical scenario - one measurement per transmission
    print(f"\nTest 1 - Typical scenario:")
    daily_energy, breakdown = sensor.calculate_daily_energy_mah(
        measurements_per_day=96,  # Every 15 minutes
        resolution_bits=12,
        temperature=20.0
    )
    print(f"Measurements: 96/day (every 15 min), 12-bit resolution, 20°C")
    print(f"Active Current: {breakdown['active_current_mA']:.2f} mA")
    print(f"Standby Current: {breakdown['standby_current_uA']:.3f} µA")
    print(f"Conversion Time: {breakdown['conversion_time_ms']:.1f} ms")
    print(f"Daily Energy: {daily_energy:.6f} mAh")
    print(f"  - Measurements: {breakdown['measurement_energy_percent']:.1f}%")
    print(f"  - Standby: {breakdown['standby_energy_percent']:.1f}%")
    
    # Test 2: High frequency measurements
    print(f"\nTest 2 - High frequency measurements:")
    daily_energy, breakdown = sensor.calculate_daily_energy_mah(
        measurements_per_day=288,  # Every 5 minutes
        resolution_bits=12,
        temperature=-20.0
    )
    print(f"Measurements: 288/day (every 5 min), 12-bit resolution, -20°C")
    print(f"Active Current: {breakdown['active_current_mA']:.2f} mA")
    print(f"Standby Current: {breakdown['standby_current_uA']:.3f} µA")
    print(f"Daily Energy: {daily_energy:.6f} mAh")
    print(f"  - Measurements: {breakdown['measurement_energy_percent']:.1f}%")
    print(f"  - Standby: {breakdown['standby_energy_percent']:.1f}%")
    
    # Test 3: Lower resolution for power saving
    print(f"\nTest 3 - Lower resolution for power saving:")
    daily_energy, breakdown = sensor.calculate_daily_energy_mah(
        measurements_per_day=96,
        resolution_bits=9,  # Faster conversion
        temperature=20.0
    )
    print(f"Measurements: 96/day, 9-bit resolution (power saving), 20°C")
    print(f"Conversion Time: {breakdown['conversion_time_ms']:.1f} ms")
    print(f"Daily Energy: {daily_energy:.6f} mAh")
    print(f"  - Measurements: {breakdown['measurement_energy_percent']:.1f}%")
    print(f"  - Standby: {breakdown['standby_energy_percent']:.1f}%")


if __name__ == "__main__":
    test_max31820_model()

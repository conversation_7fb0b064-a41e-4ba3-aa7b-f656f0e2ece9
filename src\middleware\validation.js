/**
 * Data Validation Middleware - COLDRA Factory Tool
 * Input validation, sanitization, and security checks
 */

const logger = require('../utils/logger');

class ValidationMiddleware {
    // Validate device creation data
    static validateDeviceData(req, res, next) {
        try {
            const { serial_number, deveui, appeui, appkey, region, firmware_version, operator_id } = req.body;
            const errors = [];

            // Required field validation
            if (!serial_number) errors.push('serial_number is required');
            if (!deveui) errors.push('deveui is required');
            // TODO: TEMPORARY - AppEUI/App<PERSON>ey made optional until file loading system implemented
            // MUST IMPLEMENT: TTN credential file loading system - credentials from files, not manual input
            // if (!appkey) errors.push('appkey is required');
            if (!region) errors.push('region is required');
            // TODO: TEMPORARY - firmware_version made optional until file system implemented
            // if (!firmware_version) errors.push('firmware_version is required');
            if (!operator_id) errors.push('operator_id is required');

            // Format validation
            if (serial_number && !ValidationMiddleware.isValidSerialNumber(serial_number)) {
                errors.push('serial_number format invalid (expected: DDMMYY-R-NNN)');
            }

            if (deveui && !ValidationMiddleware.isValidDevEUI(deveui)) {
                errors.push('deveui must be 16 hex characters');
            }

            if (appeui && !ValidationMiddleware.isValidAppEUI(appeui)) {
                errors.push('appeui must be 16 hex characters');
            }

            if (appkey && !ValidationMiddleware.isValidAppKey(appkey)) {
                errors.push('appkey must be 32 hex characters');
            }

            if (region && !ValidationMiddleware.isValidRegion(region)) {
                errors.push('region must be US915, EU868, or IN865');
            }

            if (firmware_version && !ValidationMiddleware.isValidFirmwareVersion(firmware_version)) {
                errors.push('firmware_version format invalid (expected: X.Y.Z)');
            }

            if (operator_id && !ValidationMiddleware.isValidOperatorId(operator_id)) {
                errors.push('operator_id contains invalid characters');
            }

            if (errors.length > 0) {
                logger.warn('Device validation failed', { errors, body: req.body });
                return res.status(400).json({
                    message: 'Validation failed',
                    errors: errors,
                    timestamp: new Date().toISOString()
                });
            }

            // Sanitize data
            req.body = ValidationMiddleware.sanitizeDeviceData(req.body);
            next();

        } catch (error) {
            logger.error('Validation middleware error', error);
            res.status(500).json({
                message: 'Validation error',
                error: error.message,
                timestamp: new Date().toISOString()
            });
        }
    }

    // Validate serial number generation request
    static validateSerialGeneration(req, res, next) {
        try {
            const { region } = req.body;
            const errors = [];

            if (region && !ValidationMiddleware.isValidRegion(region)) {
                errors.push('region must be US915, EU868, or IN865');
            }

            if (errors.length > 0) {
                return res.status(400).json({
                    message: 'Validation failed',
                    errors: errors,
                    timestamp: new Date().toISOString()
                });
            }

            // Set default region if not provided
            if (!region) {
                req.body.region = 'US915';
            }

            next();

        } catch (error) {
            logger.error('Serial generation validation error', error);
            res.status(500).json({
                message: 'Validation error',
                error: error.message,
                timestamp: new Date().toISOString()
            });
        }
    }

    // Validate status update request
    static validateStatusUpdate(req, res, next) {
        try {
            const { serial_number, status_type, status, operator_id } = req.body;
            const errors = [];

            if (!serial_number) errors.push('serial_number is required');
            if (!status_type) errors.push('status_type is required');
            if (!status) errors.push('status is required');
            if (!operator_id) errors.push('operator_id is required');

            if (serial_number && !ValidationMiddleware.isValidSerialNumber(serial_number)) {
                errors.push('serial_number format invalid');
            }

            if (status_type && !['programming_status', 'provisioning_status', 'test_status'].includes(status_type)) {
                errors.push('status_type must be programming_status, provisioning_status, or test_status');
            }

            if (status && !ValidationMiddleware.isValidStatus(status)) {
                errors.push('status must be PENDING, IN_PROGRESS, COMPLETE, FAILED, PASS, or FAIL');
            }

            if (operator_id && !ValidationMiddleware.isValidOperatorId(operator_id)) {
                errors.push('operator_id contains invalid characters');
            }

            if (errors.length > 0) {
                return res.status(400).json({
                    message: 'Validation failed',
                    errors: errors,
                    timestamp: new Date().toISOString()
                });
            }

            next();

        } catch (error) {
            logger.error('Status update validation error', error);
            res.status(500).json({
                message: 'Validation error',
                error: error.message,
                timestamp: new Date().toISOString()
            });
        }
    }

    // Security middleware - rate limiting and basic protection
    static securityCheck(req, res, next) {
        try {
            // Basic security headers
            res.setHeader('X-Content-Type-Options', 'nosniff');
            res.setHeader('X-Frame-Options', 'DENY');
            res.setHeader('X-XSS-Protection', '1; mode=block');

            // Log all POST/PUT/DELETE requests
            if (['POST', 'PUT', 'DELETE'].includes(req.method)) {
                logger.info('API request', {
                    method: req.method,
                    path: req.path,
                    ip: req.ip,
                    user_agent: req.get('User-Agent')
                });
            }

            next();

        } catch (error) {
            logger.error('Security middleware error', error);
            res.status(500).json({
                message: 'Security check failed',
                timestamp: new Date().toISOString()
            });
        }
    }

    // Validation helper functions
    static isValidSerialNumber(serial) {
        return /^(\d{6})-([UEI])-(\d{3})$/.test(serial);
    }

    static isValidDevEUI(deveui) {
        return /^[0-9A-Fa-f]{16}$/.test(deveui);
    }

    static isValidAppEUI(appeui) {
        return /^[0-9A-Fa-f]{16}$/.test(appeui);
    }

    static isValidAppKey(appkey) {
        return /^[0-9A-Fa-f]{32}$/.test(appkey);
    }

    static isValidRegion(region) {
        return ['US915', 'EU868', 'IN865'].includes(region);
    }

    static isValidFirmwareVersion(version) {
        return /^\d+\.\d+\.\d+$/.test(version);
    }

    static isValidOperatorId(operatorId) {
        return /^[A-Z0-9_]{3,20}$/.test(operatorId);
    }

    static isValidStatus(status) {
        return ['PENDING', 'IN_PROGRESS', 'COMPLETE', 'FAILED', 'PASS', 'FAIL'].includes(status);
    }

    // Data sanitization
    static sanitizeDeviceData(data) {
        const sanitized = { ...data };

        // Uppercase hex values
        if (sanitized.deveui) sanitized.deveui = sanitized.deveui.toUpperCase();
        if (sanitized.appeui) sanitized.appeui = sanitized.appeui.toUpperCase();
        if (sanitized.appkey) sanitized.appkey = sanitized.appkey.toUpperCase();

        // Uppercase region and operator
        if (sanitized.region) sanitized.region = sanitized.region.toUpperCase();
        if (sanitized.operator_id) sanitized.operator_id = sanitized.operator_id.toUpperCase();

        // Trim strings
        Object.keys(sanitized).forEach(key => {
            if (typeof sanitized[key] === 'string') {
                sanitized[key] = sanitized[key].trim();
            }
        });

        return sanitized;
    }

    // Test validation functions
    static testValidation(req, res) {
        try {
            const testCases = [
                {
                    name: 'Valid Serial Number',
                    test: '070925-U-001',
                    validator: 'isValidSerialNumber',
                    expected: true
                },
                {
                    name: 'Invalid Serial Number',
                    test: '07092-U-001',
                    validator: 'isValidSerialNumber',
                    expected: false
                },
                {
                    name: 'Valid DevEUI',
                    test: '1234567890ABCDEF',
                    validator: 'isValidDevEUI',
                    expected: true
                },
                {
                    name: 'Invalid DevEUI',
                    test: '1234567890ABCDEG',
                    validator: 'isValidDevEUI',
                    expected: false
                },
                {
                    name: 'Valid Region',
                    test: 'US915',
                    validator: 'isValidRegion',
                    expected: true
                },
                {
                    name: 'Invalid Region',
                    test: 'XX999',
                    validator: 'isValidRegion',
                    expected: false
                }
            ];

            const results = testCases.map(testCase => {
                const result = ValidationMiddleware[testCase.validator](testCase.test);
                return {
                    ...testCase,
                    actual: result,
                    passed: result === testCase.expected
                };
            });

            const allPassed = results.every(r => r.passed);

            logger.info('Validation test requested', { endpoint: '/api/validation/test' });

            // Check if request wants HTML format
            if (req.headers.accept && req.headers.accept.includes('text/html')) {
                let html = `
                    <html>
                    <head>
                        <title>Validation Test Results</title>
                        <style>
                            body { font-family: Arial, sans-serif; background: #006E74; color: white; padding: 20px; }
                            .container { max-width: 800px; margin: 0 auto; background: rgba(0,151,172,0.1); padding: 20px; border-radius: 10px; }
                            h1 { color: #0097AC; }
                            .test-case { background: rgba(255,255,255,0.1); padding: 10px; margin: 10px 0; border-radius: 5px; }
                            .pass { border-left: 5px solid #00ff00; }
                            .fail { border-left: 5px solid #ff0000; }
                            .summary { background: #0097AC; padding: 15px; border-radius: 5px; margin: 20px 0; }
                            .back { color: #0097AC; text-decoration: none; }
                        </style>
                    </head>
                    <body>
                        <div class="container">
                            <h1>🔍 Data Validation Test Results</h1>
                            <div class="summary">
                                <h3>Overall Result: ${allPassed ? '✅ ALL TESTS PASSED' : '❌ SOME TESTS FAILED'}</h3>
                                <p>Passed: ${results.filter(r => r.passed).length} / ${results.length}</p>
                            </div>`;

                results.forEach(result => {
                    html += `
                            <div class="test-case ${result.passed ? 'pass' : 'fail'}">
                                <strong>${result.name}:</strong> ${result.passed ? '✅ PASS' : '❌ FAIL'}<br>
                                <small>Input: "${result.test}" | Expected: ${result.expected} | Got: ${result.actual}</small>
                            </div>`;
                });

                html += `
                            <p><a href="/" class="back">← Back to Main Page</a></p>
                        </div>
                    </body>
                    </html>`;

                res.send(html);
            } else {
                res.json({
                    message: 'Validation test completed',
                    overall_result: allPassed ? 'PASS' : 'FAIL',
                    test_results: results,
                    summary: {
                        total_tests: results.length,
                        passed: results.filter(r => r.passed).length,
                        failed: results.filter(r => !r.passed).length
                    },
                    timestamp: new Date().toISOString()
                });
            }

        } catch (error) {
            logger.error('Validation test failed', error);
            res.status(500).json({
                message: 'Validation test failed',
                error: error.message,
                timestamp: new Date().toISOString()
            });
        }
    }
}

module.exports = ValidationMiddleware;

// Comprehensive Device Management Test Suite
import axios from 'axios';

const API_BASE = 'http://localhost:3000';

// Generate unique test data to avoid database conflicts
const generateUniqueTestCases = () => {
  const timestamp = Date.now();
  const baseTime = timestamp.toString().slice(-6);

  return [
    // Valid cases with unique data
    {
      name: 'Valid US915 Device',
      data: {
        serial_number: `070925-U-${baseTime.slice(0,3)}`,
        deveui: (timestamp + 1).toString(16).padStart(16, '0').toUpperCase(),
        region: 'US915',
        operator_id: 'OP001'
      },
      shouldPass: true
    },
    {
      name: 'Valid EU868 Device',
      data: {
        serial_number: `070925-E-${baseTime.slice(1,4)}`,
        deveui: (timestamp + 2).toString(16).padStart(16, '0').toUpperCase(),
        region: 'EU868',
        operator_id: 'OP002'
      },
      shouldPass: true
    },
    {
      name: 'Valid IN865 Device',
      data: {
        serial_number: `070925-I-${baseTime.slice(2,5)}`,
        deveui: (timestamp + 3).toString(16).padStart(16, '0').toUpperCase(),
        region: 'IN865',
        operator_id: 'OP003'
      },
      shouldPass: true
    },

    // Invalid cases (these don't need unique data since they should fail validation)
    {
      name: 'Region Mismatch - U serial with EU868',
      data: {
        serial_number: '070925-U-999',
        deveui: 'AAAAAAAAAAAAAAAA',
        region: 'EU868',
        operator_id: 'OP001'
      },
      shouldPass: false,
      expectedError: 'Region mismatch'
    },
  {
    name: 'Invalid DevEUI - Too Short',
    data: {
      serial_number: '070925-U-001',
      deveui: '12345678',
      region: 'US915',
      operator_id: 'OP001'
    },
    shouldPass: false,
    expectedError: 'DevEUI must be exactly 16'
  },
  {
    name: 'Invalid Serial Format',
    data: {
      serial_number: '07-09-25-U-001',
      deveui: '1234567890ABCDEF',
      region: 'US915',
      operator_id: 'OP001'
    },
    shouldPass: false,
    expectedError: 'Serial number must be in format'
  },
  {
    name: 'Empty DevEUI',
    data: {
      serial_number: '070925-U-001',
      deveui: '',
      region: 'US915',
      operator_id: 'OP001'
    },
    shouldPass: false,
    expectedError: 'DevEUI is required'
  }
  ];
};

// Validation function (same as in component)
const validateDevice = (device) => {
  const errors = [];
  
  // Serial number validation (DDMMYY-R-NNN format)
  const serialRegex = /^\d{6}-[UEI]-\d{3}$/;
  if (!serialRegex.test(device.serial_number)) {
    errors.push('Serial number must be in format DDMMYY-R-NNN (e.g., 070925-U-001)');
  } else {
    // Check region consistency
    const regionLetter = device.serial_number.charAt(7); // Get the R from DDMMYY-R-NNN
    const expectedRegion = regionLetter === 'U' ? 'US915' : regionLetter === 'E' ? 'EU868' : 'IN865';
    if (device.region !== expectedRegion) {
      errors.push(`Region mismatch: Serial "${device.serial_number}" indicates ${expectedRegion}, but ${device.region} is selected`);
    }
  }
  
  // DevEUI validation (16 hex characters)
  if (!device.deveui || device.deveui.trim() === '') {
    errors.push('DevEUI is required');
  } else {
    const deveuiRegex = /^[0-9A-Fa-f]{16}$/;
    if (!deveuiRegex.test(device.deveui)) {
      errors.push('DevEUI must be exactly 16 hexadecimal characters (0-9, A-F)');
    }
  }
  
  // Operator ID validation
  if (!device.operator_id || device.operator_id.trim() === '') {
    errors.push('Operator ID is required');
  }
  
  return errors;
};

// Test API endpoint
const testAPIEndpoint = async () => {
  try {
    const response = await axios.get(`${API_BASE}/api/devices/test`);
    console.log('✅ API Endpoint Test:', response.status === 200 ? 'PASS' : 'FAIL');
    return response.status === 200;
  } catch (error) {
    console.log('❌ API Endpoint Test: FAIL -', error.message);
    return false;
  }
};

// Run comprehensive test suite
export const runDeviceTestSuite = async () => {
  console.log('🧪 Starting Device Management Test Suite...\n');

  // Generate unique test cases for this run
  const testCases = generateUniqueTestCases();

  // Test API connectivity
  const apiWorking = await testAPIEndpoint();
  if (!apiWorking) {
    console.log('❌ API not working - stopping tests');
    return false;
  }
  
  let passCount = 0;
  let failCount = 0;
  
  // Test validation logic
  console.log('📋 Testing Validation Logic:');
  testCases.forEach((testCase, index) => {
    const errors = validateDevice(testCase.data);
    const hasErrors = errors.length > 0;
    
    if (testCase.shouldPass && !hasErrors) {
      console.log(`✅ Test ${index + 1}: ${testCase.name} - PASS`);
      passCount++;
    } else if (!testCase.shouldPass && hasErrors) {
      const errorMatch = testCase.expectedError ? 
        errors.some(err => err.includes(testCase.expectedError)) : true;
      if (errorMatch) {
        console.log(`✅ Test ${index + 1}: ${testCase.name} - PASS (Expected error: ${errors[0]})`);
        passCount++;
      } else {
        console.log(`❌ Test ${index + 1}: ${testCase.name} - FAIL (Wrong error: ${errors[0]})`);
        failCount++;
      }
    } else {
      console.log(`❌ Test ${index + 1}: ${testCase.name} - FAIL`);
      if (hasErrors) console.log(`   Errors: ${errors.join(', ')}`);
      failCount++;
    }
  });
  
  console.log(`\n📊 Test Results: ${passCount} PASS, ${failCount} FAIL`);
  return failCount === 0;
};

// Quick test for user's specific case
export const testUserCase = () => {
  console.log('🔍 Testing User\'s Specific Case:');
  
  const userCase = {
    serial_number: '070925-U-001',
    deveui: '1234567890ABCDEF',
    region: 'US915',
    operator_id: 'OP001'
  };
  
  const errors = validateDevice(userCase);
  if (errors.length === 0) {
    console.log('✅ User case should work - no validation errors');
    return true;
  } else {
    console.log('❌ User case has validation errors:');
    errors.forEach(error => console.log(`   - ${error}`));
    return false;
  }
};

export default { runDeviceTestSuite, testUserCase };

import React, { useState, useEffect } from 'react';
import axios from 'axios';
import { runDeviceTestSuite, testUserCase } from '../utils/deviceTestSuite';

const DeviceManagement = () => {
  const [devices, setDevices] = useState([]);
  const [showCreateForm, setShowCreateForm] = useState(false);
  const [editingDevice, setEditingDevice] = useState(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');
  const [searchTerm, setSearchTerm] = useState('');
  // TODO: TEMPORARY - Remove AppEUI/AppKey when file loading system implemented
  const [newDevice, setNewDevice] = useState({
    serial_number: '',
    deveui: '',
    region: 'US915',
    operator_id: 'OP001'
  });

  useEffect(() => {
    fetchDevices();
  }, []);

  const fetchDevices = async () => {
    setLoading(true);
    try {
      const response = await axios.get('http://localhost:3000/api/devices/search');

      if (response.data && response.data.devices) {
        setDevices(response.data.devices);
      } else {
        setDevices([]);
      }
    } catch (error) {
      setError('Failed to fetch devices: ' + error.message);
    } finally {
      setLoading(false);
    }
  };

  const validateDevice = (device) => {
    const errors = [];

    // Serial number validation (DDMMYY-R-NNN format)
    const serialRegex = /^\d{6}-[UEI]-\d{3}$/;
    if (!serialRegex.test(device.serial_number)) {
      errors.push('Serial number must be in format DDMMYY-R-NNN (e.g., 070925-U-001)');
    } else {
      // Check region consistency
      const regionLetter = device.serial_number.charAt(7); // Get the R from DDMMYY-R-NNN
      const expectedRegion = regionLetter === 'U' ? 'US915' : regionLetter === 'E' ? 'EU868' : 'IN865';
      if (device.region !== expectedRegion) {
        errors.push(`Region mismatch: Serial "${device.serial_number}" indicates ${expectedRegion}, but ${device.region} is selected`);
      }
    }

    // DevEUI validation (16 hex characters)
    if (!device.deveui || device.deveui.trim() === '') {
      errors.push('DevEUI is required');
    } else {
      const deveuiRegex = /^[0-9A-Fa-f]{16}$/;
      if (!deveuiRegex.test(device.deveui)) {
        errors.push('DevEUI must be exactly 16 hexadecimal characters (0-9, A-F)');
      }
    }

    // TODO: TEMPORARY - AppEUI/AppKey validation removed until file loading system implemented
    // MUST IMPLEMENT: TTN credential file loading system - credentials from files, not manual input

    // Operator ID validation
    if (!device.operator_id || device.operator_id.trim() === '') {
      errors.push('Operator ID is required');
    }

    return errors;
  };

  const handleCreateDevice = async (e) => {
    e.preventDefault();
    setError('');
    setSuccess('');

    const validationErrors = validateDevice(newDevice);

    if (validationErrors.length > 0) {
      setError(validationErrors.join('. '));
      return;
    }

    setLoading(true);
    try {
      const response = await axios.post('http://localhost:3000/api/devices/create', newDevice);

      setSuccess('Device created successfully!');
      setShowCreateForm(false);
      setNewDevice({
        serial_number: '',
        deveui: '',
        region: 'US915',
        operator_id: 'OP001'
      });
      await fetchDevices(); // Refresh the list
    } catch (error) {
      console.error('🔍 Debug - API Error:', error);
      console.error('🔍 Debug - Error response:', error.response?.data);
      console.error('🔍 Debug - Error status:', error.response?.status);

      const errorMessage = error.response?.data?.error ||
                          error.response?.data?.message ||
                          error.message ||
                          'Unknown error occurred';
      setError(`Failed to create device: ${errorMessage}`);
    } finally {
      setLoading(false);
    }
  };

  const handleEditDevice = async (e) => {
    e.preventDefault();
    setError('');
    setSuccess('');

    const validationErrors = validateDevice(editingDevice);
    if (validationErrors.length > 0) {
      setError(validationErrors.join('. '));
      return;
    }

    setLoading(true);
    try {
      await axios.put(`http://localhost:3000/api/devices/${editingDevice.id}`, editingDevice);
      setSuccess('Device updated successfully!');
      setEditingDevice(null);
      fetchDevices(); // Refresh the list
    } catch (error) {
      setError('Failed to update device: ' + (error.response?.data?.error || error.message));
    } finally {
      setLoading(false);
    }
  };

  const handleDeleteDevice = async (deviceId) => {
    if (!window.confirm('Are you sure you want to delete this device? This action cannot be undone.')) {
      return;
    }

    setLoading(true);
    try {
      await axios.delete(`http://localhost:3000/api/devices/${deviceId}`);
      setSuccess('Device deleted successfully!');
      fetchDevices(); // Refresh the list
    } catch (error) {
      setError('Failed to delete device: ' + (error.response?.data?.error || error.message));
    } finally {
      setLoading(false);
    }
  };

  const filteredDevices = devices.filter(device =>
    device.serial_number?.toLowerCase().includes(searchTerm.toLowerCase()) ||
    device.deveui?.toLowerCase().includes(searchTerm.toLowerCase()) ||
    device.region?.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const testAPIDirectly = async () => {
    console.clear();
    console.log('🧪 Testing API Endpoint Directly...');

    try {
      // Generate unique test data
      const timestamp = Date.now();
      const uniqueDevEUI = timestamp.toString(16).padStart(16, '0').toUpperCase();
      const uniqueSerial = `070925-U-${String(timestamp).slice(-3)}`;

      const testData = {
        serial_number: uniqueSerial,
        deveui: uniqueDevEUI,
        region: 'US915',
        operator_id: 'OP001'
      };

      console.log('🔍 Testing with data:', testData);

      const response = await axios.post('http://localhost:3000/api/devices/create', testData);
      console.log('✅ API Response:', response.data);
      setSuccess('✅ API test successful! Device creation works.');

      // Refresh device list after successful test
      fetchDevices();

    } catch (error) {
      console.error('❌ API Test Failed:', error);
      console.error('❌ Error details:', {
        status: error.response?.status,
        statusText: error.response?.statusText,
        data: error.response?.data,
        message: error.message
      });

      setError(`❌ API test failed: ${error.response?.status} - ${error.response?.data?.error || error.message}`);
    }
  };

  const runTests = async () => {
    console.clear();
    console.log('🧪 Running Device Management Tests...');

    // Test user's specific case first
    const userCaseWorks = testUserCase();

    // Run full test suite
    const allTestsPass = await runDeviceTestSuite();

    if (userCaseWorks && allTestsPass) {
      setSuccess('✅ All tests passed! Device management should work correctly.');
    } else {
      setError('❌ Some tests failed. Check browser console for details.');
    }
  };

  const fillValidData = () => {
    // Generate unique DevEUI and Sequential Serial Number
    const timestamp = Date.now();
    const uniqueDevEUI = timestamp.toString(16).padStart(16, '0').toUpperCase();

    // Generate sequential serial number based on current device count + 1
    const nextSerialNumber = devices.length + 1;
    const sequentialSerial = `070925-U-${String(nextSerialNumber).padStart(3, '0')}`;

    setNewDevice({
      serial_number: sequentialSerial,
      deveui: uniqueDevEUI,
      region: 'US915',
      operator_id: 'OP001'
    });
    setSuccess('✅ Valid test data filled in form');
  };

  return (
    <div className="device-management">
      <div className="page-header">
        <h1>📱 Device Management</h1>
        <p>Create, view, edit, and delete device records</p>
      </div>

      {error && (
        <div className="alert alert-error">
          <strong>Error:</strong> {error}
          <button className="alert-close" onClick={() => setError('')}>×</button>
        </div>
      )}

      {success && (
        <div className="alert alert-success">
          <strong>Success:</strong> {success}
          <button className="alert-close" onClick={() => setSuccess('')}>×</button>
        </div>
      )}

      <div className="page-actions">
        <button
          className="btn btn-primary"
          onClick={() => setShowCreateForm(!showCreateForm)}
          disabled={loading}
        >
          {showCreateForm ? 'Cancel' : '+ Create New Device'}
        </button>

        <button
          className="btn btn-secondary"
          onClick={testAPIDirectly}
          disabled={loading}
          style={{ marginLeft: '10px' }}
        >
          🔧 Test API
        </button>

        <button
          className="btn btn-secondary"
          onClick={runTests}
          disabled={loading}
          style={{ marginLeft: '10px' }}
        >
          🧪 Run Tests
        </button>

        {showCreateForm && (
          <button
            className="btn btn-secondary"
            onClick={fillValidData}
            disabled={loading}
            style={{ marginLeft: '10px' }}
          >
            📝 Fill Valid Data
          </button>
        )}

        <div className="search-box">
          <input
            type="text"
            className="form-input"
            placeholder="🔍 Search devices by serial, DevEUI, or region..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
          />
        </div>
      </div>

      {showCreateForm && (
        <div className="card">
          <div className="card-title">✨ Create New Device</div>
          <form onSubmit={handleCreateDevice} className="device-form">
            <div className="form-row">
              <div className="form-group">
                <label className="form-label">1. Serial Number</label>
                <input
                  type="text"
                  className="form-input"
                  placeholder="e.g., 070925-U-001 (DDMMYY-R-NNN)"
                  value={newDevice.serial_number}
                  onChange={(e) => setNewDevice({...newDevice, serial_number: e.target.value.toUpperCase()})}
                  required
                  disabled={loading}
                />
                <small className="form-help">Format: DDMMYY-R-NNN (R = U/E/I for US915/EU868/IN865)</small>
              </div>
              <div className="form-group">
                <label className="form-label">2. DevEUI</label>
                <input
                  type="text"
                  className="form-input"
                  placeholder="16 hex characters (e.g., 1234567890ABCDEF)"
                  value={newDevice.deveui}
                  onChange={(e) => setNewDevice({...newDevice, deveui: e.target.value.toUpperCase()})}
                  required
                  disabled={loading}
                  maxLength={16}
                />
                <small className="form-help">Exactly 16 hexadecimal characters (0-9, A-F)</small>
              </div>
            </div>
            {/* TODO: TEMPORARY - AppEUI/AppKey fields removed until file loading system implemented */}
            {/* MUST IMPLEMENT: TTN credential file loading system - credentials from files, not manual input */}
            <div className="form-row">
              <div className="form-group">
                <label className="form-label">3. Region</label>
                <select
                  className="form-input"
                  value={newDevice.region}
                  onChange={(e) => setNewDevice({...newDevice, region: e.target.value})}
                  disabled={loading}
                >
                  <option value="US915">US915 - United States</option>
                  <option value="EU868">EU868 - Europe</option>
                  <option value="IN865">IN865 - India</option>
                </select>
              </div>
              <div className="form-group">
                <label className="form-label">4. Operator ID</label>
                <input
                  type="text"
                  className="form-input"
                  placeholder="e.g., OP001"
                  value={newDevice.operator_id}
                  onChange={(e) => setNewDevice({...newDevice, operator_id: e.target.value})}
                  required
                  disabled={loading}
                />
                <small className="form-help">Operator identifier for tracking</small>
              </div>
            </div>
            <div className="form-actions">
              <button type="submit" className="btn btn-primary" disabled={loading}>
                {loading ? '⏳ Creating...' : '✅ Create Device'}
              </button>
              <button
                type="button"
                className="btn btn-secondary"
                onClick={() => setShowCreateForm(false)}
                disabled={loading}
              >
                Cancel
              </button>
            </div>
          </form>
        </div>
      )}

      <div className="card">
        <div className="card-title">
          📋 Device Records ({filteredDevices.length} {filteredDevices.length === 1 ? 'device' : 'devices'})
        </div>

        {loading && (
          <div className="loading">⏳ Loading devices...</div>
        )}

        {!loading && filteredDevices.length === 0 && !searchTerm && (
          <div className="empty-state">
            <p>📱 No devices found. Create your first device to get started.</p>
          </div>
        )}

        {!loading && filteredDevices.length === 0 && searchTerm && (
          <div className="empty-state">
            <p>🔍 No devices match your search criteria.</p>
          </div>
        )}

        {!loading && filteredDevices.length > 0 && (
          <div className="devices-table">
            <div className="table-header">
              <div className="table-row header-row">
                <div className="table-cell">#</div>
                <div className="table-cell">Serial Number</div>
                <div className="table-cell">DevEUI</div>
                <div className="table-cell">Region</div>
                <div className="table-cell">Status</div>
                <div className="table-cell">Operator</div>
                <div className="table-cell">Actions</div>
              </div>
            </div>
            <div className="table-body">
              {filteredDevices.map((device, index) => (
                <div key={device.id || index} className="table-row">
                  <div className="table-cell">
                    <strong>{index + 1}</strong>
                  </div>
                  <div className="table-cell">
                    <strong>{device.serial_number}</strong>
                  </div>
                  <div className="table-cell">
                    <code>{device.deveui}</code>
                  </div>
                  <div className="table-cell">
                    <span className={`region-badge region-${device.region?.toLowerCase()}`}>
                      {device.region}
                    </span>
                  </div>
                  <div className="table-cell">
                    <span className={`status-badge status-${device.status || 'created'}`}>
                      {device.status || 'Created'}
                    </span>
                  </div>
                  <div className="table-cell">
                    {device.operator_id}
                  </div>
                  <div className="table-cell">
                    <div className="action-buttons">
                      <button
                        className="btn-action btn-edit"
                        onClick={() => setEditingDevice({...device})}
                        disabled={loading}
                        title="Edit Device"
                      >
                        ✏️
                      </button>
                      <button
                        className="btn-action btn-delete"
                        onClick={() => handleDeleteDevice(device.id)}
                        disabled={loading}
                        title="Delete Device"
                      >
                        🗑️
                      </button>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}
      </div>

      {editingDevice && (
        <div className="modal-overlay">
          <div className="modal">
            <div className="modal-header">
              <h3>✏️ Edit Device</h3>
              <button
                className="modal-close"
                onClick={() => setEditingDevice(null)}
                disabled={loading}
              >
                ×
              </button>
            </div>
            <form onSubmit={handleEditDevice} className="device-form">
              <div className="form-row">
                <div className="form-group">
                  <label className="form-label">1. Serial Number</label>
                  <input
                    type="text"
                    className="form-input"
                    placeholder="e.g., 070925-U-001 (DDMMYY-R-NNN)"
                    value={editingDevice.serial_number}
                    onChange={(e) => setEditingDevice({...editingDevice, serial_number: e.target.value.toUpperCase()})}
                    required
                    disabled={loading}
                  />
                </div>
                <div className="form-group">
                  <label className="form-label">2. DevEUI</label>
                  <input
                    type="text"
                    className="form-input"
                    placeholder="16 hex characters"
                    value={editingDevice.deveui}
                    onChange={(e) => setEditingDevice({...editingDevice, deveui: e.target.value.toUpperCase()})}
                    required
                    disabled={loading}
                    maxLength={16}
                  />
                </div>
              </div>
              <div className="form-row">
                <div className="form-group">
                  <label className="form-label">3. Region</label>
                  <select
                    className="form-input"
                    value={editingDevice.region}
                    onChange={(e) => setEditingDevice({...editingDevice, region: e.target.value})}
                    disabled={loading}
                  >
                    <option value="US915">US915 - United States</option>
                    <option value="EU868">EU868 - Europe</option>
                    <option value="IN865">IN865 - India</option>
                  </select>
                </div>
                <div className="form-group">
                  <label className="form-label">4. Operator ID</label>
                  <input
                    type="text"
                    className="form-input"
                    value={editingDevice.operator_id}
                    onChange={(e) => setEditingDevice({...editingDevice, operator_id: e.target.value})}
                    required
                    disabled={loading}
                  />
                </div>
              </div>
              <div className="form-actions">
                <button type="submit" className="btn btn-primary" disabled={loading}>
                  {loading ? '⏳ Updating...' : '✅ Update Device'}
                </button>
                <button
                  type="button"
                  className="btn btn-secondary"
                  onClick={() => setEditingDevice(null)}
                  disabled={loading}
                >
                  Cancel
                </button>
              </div>
            </form>
          </div>
        </div>
      )}

      <div className="card">
        <div className="card-title">📋 Available Device Operations (7 endpoints)</div>
        <div className="operations-list">
          <div className="operation-item">
            <span className="operation-number">10.</span>
            <span className="operation-name">GET /api/devices/test</span>
            <span className="operation-desc">Test device record management system</span>
          </div>
          <div className="operation-item">
            <span className="operation-number">11.</span>
            <span className="operation-name">GET /api/devices/stats</span>
            <span className="operation-desc">Get device production statistics</span>
          </div>
          <div className="operation-item">
            <span className="operation-number">12.</span>
            <span className="operation-name">POST /api/devices/create</span>
            <span className="operation-desc">Create new device record</span>
          </div>
          <div className="operation-item">
            <span className="operation-number">13.</span>
            <span className="operation-name">GET /api/devices/:id</span>
            <span className="operation-desc">Get device by ID</span>
          </div>
          <div className="operation-item">
            <span className="operation-number">14.</span>
            <span className="operation-name">PUT /api/devices/:id</span>
            <span className="operation-desc">Update device record</span>
          </div>
          <div className="operation-item">
            <span className="operation-number">15.</span>
            <span className="operation-name">DELETE /api/devices/:id</span>
            <span className="operation-desc">Delete device record</span>
          </div>
          <div className="operation-item">
            <span className="operation-number">16.</span>
            <span className="operation-name">GET /api/devices/search</span>
            <span className="operation-desc">Search devices by criteria</span>
          </div>
        </div>
      </div>
    </div>
  );
};

export default DeviceManagement;

/* COLDRA Battery Life Assessment Tool - Single Page "What If" Interface */

.app {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
  margin: 0;
  padding: 0;
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.loading-screen {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  height: 100vh;
  color: white;
  text-align: center;
}

.loading-screen h2 {
  font-size: 2rem;
  margin-bottom: 1rem;
}

.app-header {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  padding: 1.5rem 2rem;
  color: white;
  text-align: center;
  border-bottom: 1px solid rgba(255, 255, 255, 0.2);
}

.app-header h1 {
  margin: 0 0 0.5rem 0;
  font-size: 2.5rem;
  font-weight: 700;
}

.app-header p {
  margin: 0;
  font-size: 1.1rem;
  opacity: 0.9;
}

.main-content {
  display: flex;
  gap: 2rem;
  padding: 2rem;
  max-width: 1400px;
  margin: 0 auto;
  min-height: calc(100vh - 120px);
}

/* LEFT PANEL: Parameter Controls */
.parameters-panel {
  flex: 1;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 12px;
  padding: 2rem;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10px);
  overflow-y: auto;
  max-height: calc(100vh - 160px);
}

.parameters-panel h2 {
  margin: 0 0 2rem 0;
  color: #2c3e50;
  font-size: 1.8rem;
  font-weight: 600;
}

.parameter-section {
  margin-bottom: 2.5rem;
  padding: 1.5rem;
  background: rgba(52, 152, 219, 0.05);
  border-radius: 8px;
  border-left: 4px solid #3498db;
}

.parameter-section h3 {
  margin: 0 0 1.5rem 0;
  color: #2c3e50;
  font-size: 1.3rem;
  font-weight: 600;
}

.parameter-control {
  margin-bottom: 1.5rem;
}

.parameter-control label {
  display: block;
  margin-bottom: 0.5rem;
  font-weight: 600;
  color: #34495e;
  font-size: 1rem;
}

.slider {
  width: 100%;
  height: 8px;
  border-radius: 4px;
  background: #ddd;
  outline: none;
  -webkit-appearance: none;
  margin-bottom: 0.5rem;
}

.slider::-webkit-slider-thumb {
  -webkit-appearance: none;
  appearance: none;
  width: 20px;
  height: 20px;
  border-radius: 50%;
  background: #3498db;
  cursor: pointer;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.2);
}

.slider::-moz-range-thumb {
  width: 20px;
  height: 20px;
  border-radius: 50%;
  background: #3498db;
  cursor: pointer;
  border: none;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.2);
}

.temperature-slider {
  background: linear-gradient(to right, #3498db 0%, #e74c3c 100%);
}

.dropdown {
  width: 100%;
  padding: 0.75rem;
  border: 2px solid #ddd;
  border-radius: 6px;
  font-size: 1rem;
  background: white;
  color: #2c3e50;
}

.dropdown:focus {
  border-color: #3498db;
  outline: none;
}

.parameter-info {
  font-size: 0.9rem;
  color: #7f8c8d;
  font-style: italic;
  margin-top: 0.25rem;
}

/* RIGHT PANEL: Results */
.results-panel {
  flex: 1;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 12px;
  padding: 2rem;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10px);
  overflow-y: auto;
  max-height: calc(100vh - 160px);
}

.results-panel h2 {
  margin: 0 0 2rem 0;
  color: #2c3e50;
  font-size: 1.8rem;
  font-weight: 600;
}

.loading-indicator {
  text-align: center;
  padding: 3rem;
  color: #7f8c8d;
  font-size: 1.2rem;
}

.battery-life-display {
  text-align: center;
  padding: 2rem;
  background: linear-gradient(135deg, #27ae60, #2ecc71);
  border-radius: 12px;
  color: white;
  margin-bottom: 2rem;
  box-shadow: 0 4px 20px rgba(46, 204, 113, 0.3);
}

.battery-life-main {
  display: flex;
  align-items: baseline;
  justify-content: center;
  gap: 0.5rem;
}

.battery-life-number {
  font-size: 4rem;
  font-weight: 700;
  line-height: 1;
}

.battery-life-unit {
  font-size: 1.5rem;
  font-weight: 600;
  opacity: 0.9;
}

.battery-life-sub {
  font-size: 1.1rem;
  opacity: 0.8;
  margin-top: 0.5rem;
}

.current-breakdown,
.battery-details {
  margin-bottom: 2rem;
  padding: 1.5rem;
  background: rgba(52, 152, 219, 0.05);
  border-radius: 8px;
  border-left: 4px solid #3498db;
}

.current-breakdown h3,
.battery-details h3 {
  margin: 0 0 1rem 0;
  color: #2c3e50;
  font-size: 1.2rem;
  font-weight: 600;
}

.breakdown-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.75rem 0;
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

.breakdown-item:last-child {
  border-bottom: none;
}

.breakdown-item span:first-child {
  color: #34495e;
  font-weight: 500;
}

.breakdown-item span:last-child {
  color: #2c3e50;
  font-weight: 600;
  font-family: 'Courier New', monospace;
}

.no-results {
  text-align: center;
  padding: 3rem;
  color: #7f8c8d;
  font-size: 1.1rem;
}

.error {
  background: #e74c3c;
  color: white;
  padding: 1rem;
  border-radius: 8px;
  margin: 1rem 0;
}

.App-footer {
  background: rgba(255, 255, 255, 0.95);
  padding: 1.5rem;
  color: #7f8c8d;
  font-size: 0.9rem;
  box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.1);
}

.App-footer p {
  margin: 0.25rem 0;
}

/* Responsive Design */
@media (max-width: 768px) {
  .calculator-container {
    grid-template-columns: 1fr;
    gap: 2rem;
    padding: 1rem;
  }

  .App-header h1 {
    font-size: 2rem;
  }

  .main-result h3 {
    font-size: 2.5rem;
  }

  .config-modal {
    width: 95%;
    margin: 1rem;
  }

  .config-tabs {
    flex-wrap: wrap;
  }

  .config-grid {
    grid-template-columns: 1fr;
    gap: 0.5rem;
  }
}

/* Header buttons */
.header-buttons {
  margin-top: 1rem;
}

.config-button {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
  color: white;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 25px;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 4px 15px rgba(240, 147, 251, 0.3);
}

.config-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(240, 147, 251, 0.4);
}

/* Configuration Modal */
.config-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.7);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
  padding: 2rem;
}

.config-modal {
  background: white;
  border-radius: 15px;
  width: 90%;
  max-width: 800px;
  max-height: 90vh;
  overflow-y: auto;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
}

.config-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 1.5rem;
  border-radius: 15px 15px 0 0;
  position: relative;
}

.config-header h2 {
  margin: 0 0 0.5rem 0;
  font-size: 1.5rem;
}

.config-warning {
  background: rgba(255, 255, 255, 0.2);
  padding: 0.75rem;
  border-radius: 8px;
  margin: 0.5rem 0 0 0;
  font-size: 0.9rem;
  border-left: 4px solid #ffd700;
}

.close-button {
  position: absolute;
  top: 1rem;
  right: 1rem;
  background: rgba(255, 255, 255, 0.2);
  color: white;
  border: none;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  font-size: 1.2rem;
  cursor: pointer;
  transition: all 0.3s ease;
}

.close-button:hover {
  background: rgba(255, 255, 255, 0.3);
  transform: scale(1.1);
}

/* Configuration Tabs */
.config-tabs {
  display: flex;
  background: #f8f9fa;
  border-bottom: 1px solid #dee2e6;
  overflow-x: auto;
}

.tab-button {
  background: none;
  border: none;
  padding: 1rem 1.5rem;
  cursor: pointer;
  font-size: 0.9rem;
  font-weight: 500;
  color: #6c757d;
  transition: all 0.3s ease;
  white-space: nowrap;
  border-bottom: 3px solid transparent;
}

.tab-button:hover {
  background: #e9ecef;
  color: #495057;
}

.tab-button.active {
  color: #667eea;
  border-bottom-color: #667eea;
  background: white;
}

/* Configuration Content */
.config-content {
  padding: 2rem;
  min-height: 400px;
}

.config-section h3 {
  color: #2c3e50;
  margin: 0 0 1rem 0;
  font-size: 1.3rem;
}

.section-note {
  background: #fff3cd;
  border: 1px solid #ffeaa7;
  border-left: 4px solid #f39c12;
  padding: 0.75rem;
  border-radius: 5px;
  margin-bottom: 1.5rem;
  font-size: 0.9rem;
}

.config-group {
  margin-bottom: 2rem;
  padding: 1rem;
  background: #f8f9fa;
  border-radius: 8px;
  border-left: 4px solid #667eea;
}

/* Additional Responsive Design for Single Page Interface */
@media (max-width: 1024px) {
  .main-content {
    flex-direction: column;
    gap: 1.5rem;
    padding: 1rem;
  }

  .parameters-panel,
  .results-panel {
    max-height: none;
  }

  .app-header h1 {
    font-size: 2rem;
  }

  .battery-life-number {
    font-size: 3rem;
  }
}

@media (max-width: 768px) {
  .app-header {
    padding: 1rem;
  }

  .app-header h1 {
    font-size: 1.8rem;
  }

  .app-header p {
    font-size: 1rem;
  }

  .parameters-panel,
  .results-panel {
    padding: 1.5rem;
  }

  .parameter-section {
    padding: 1rem;
  }

  .battery-life-number {
    font-size: 2.5rem;
  }
}
}

.config-group h4 {
  color: #495057;
  margin: 0 0 1rem 0;
  font-size: 1.1rem;
}

.config-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1rem;
  align-items: center;
}

.config-grid label {
  font-weight: 500;
  color: #495057;
}

.config-grid input[type="number"] {
  padding: 0.5rem;
  border: 1px solid #ced4da;
  border-radius: 5px;
  font-size: 0.9rem;
}

.config-grid input[type="checkbox"] {
  width: 20px;
  height: 20px;
}

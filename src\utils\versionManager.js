/**
 * Version Manager - COLDRA Factory Tool
 * SWE-compliant version tracking and management
 */

const fs = require('fs');
const path = require('path');
const crypto = require('crypto');

class VersionManager {
    constructor() {
        this.packagePath = path.join(__dirname, '../../package.json');
        this.versionFile = path.join(__dirname, '../../version.json');
        this.initializeVersion();
    }

    // Initialize version tracking
    initializeVersion() {
        try {
            // Read package.json for base version
            const packageData = JSON.parse(fs.readFileSync(this.packagePath, 'utf8'));
            this.baseVersion = packageData.version;
            
            // Create or update version.json
            this.updateVersionFile();
        } catch (error) {
            console.error('Version initialization failed:', error.message);
            this.baseVersion = '1.0.0';
        }
    }

    // Update version tracking file
    updateVersionFile() {
        const versionData = {
            version: this.baseVersion,
            build_number: this.generateBuildNumber(),
            build_date: new Date().toISOString(),
            git_commit: 'N/A', // Will be populated if git is available
            environment: process.env.NODE_ENV || 'development',
            node_version: process.version,
            components: this.getComponentVersions()
        };

        fs.writeFileSync(this.versionFile, JSON.stringify(versionData, null, 2));
        return versionData;
    }

    // Generate build number based on timestamp
    generateBuildNumber() {
        const now = new Date();
        const year = now.getFullYear().toString().slice(-2);
        const month = (now.getMonth() + 1).toString().padStart(2, '0');
        const day = now.getDate().toString().padStart(2, '0');
        const hour = now.getHours().toString().padStart(2, '0');
        const minute = now.getMinutes().toString().padStart(2, '0');
        
        return `${year}${month}${day}.${hour}${minute}`;
    }

    // Get component versions for SWE compliance
    getComponentVersions() {
        try {
            const packageData = JSON.parse(fs.readFileSync(this.packagePath, 'utf8'));
            return {
                application: packageData.version,
                dependencies: {
                    express: packageData.dependencies?.express || 'N/A',
                    winston: packageData.dependencies?.winston || 'N/A',
                    cors: packageData.dependencies?.cors || 'N/A',
                    helmet: packageData.dependencies?.helmet || 'N/A'
                }
            };
        } catch (error) {
            return { error: 'Unable to read component versions' };
        }
    }

    // Get current version information
    getVersionInfo() {
        try {
            if (fs.existsSync(this.versionFile)) {
                return JSON.parse(fs.readFileSync(this.versionFile, 'utf8'));
            } else {
                return this.updateVersionFile();
            }
        } catch (error) {
            return {
                version: this.baseVersion,
                error: 'Version file corrupted',
                timestamp: new Date().toISOString()
            };
        }
    }

    // Calculate file checksums for SWE compliance
    calculateFileChecksum(filePath) {
        try {
            const fileBuffer = fs.readFileSync(filePath);
            const hashSum = crypto.createHash('md5');
            hashSum.update(fileBuffer);
            return hashSum.digest('hex');
        } catch (error) {
            return 'N/A';
        }
    }

    // Get system manifest for SWE compliance
    getSystemManifest() {
        const coreFiles = [
            'package.json',
            'server.js',
            'src/routes/api.js',
            'src/utils/logger.js',
            'src/config/configManager.js'
        ];

        const manifest = {
            version: this.baseVersion,
            generated: new Date().toISOString(),
            files: {}
        };

        coreFiles.forEach(file => {
            const fullPath = path.join(__dirname, '../../', file);
            if (fs.existsSync(fullPath)) {
                const stats = fs.statSync(fullPath);
                manifest.files[file] = {
                    size: stats.size,
                    modified: stats.mtime.toISOString(),
                    checksum: this.calculateFileChecksum(fullPath)
                };
            }
        });

        return manifest;
    }
}

module.exports = VersionManager;

# 5 Ws & 1 H Cheat-Sheet for COLDRA Node Credentials  
*(use this to sanity-check every build)*

---

## 1. DevEUI – AC1F09FFE1234567  
*(Device EUI – 64-bit serial number)*

| Question | Answer |
|----------|--------|
| **Who** assigns it? | RAK Wireless (IEEE block owner) |
| **What** is it? | Globally-unique identifier for **this physical PCB** |
| **Where** do you find it? | Printed barcode on RAK3172 module |
| **When** is it used? | Inside every **Join-Request** radio burst |
| **Why** needed? | Lets TTN distinguish your node from millions of others |
| **How** do you use it? | `api.lorawan.deui.get()` – **read only**, never write |

> Factory action: **scan barcode → log in CSV → import to TTN** – same value for life.

---

## 2. Join <PERSON>I – FEDCBA0987654321  
*(Join <PERSON>I – 64-bit application router)*

| Question | Answer |
|----------|--------|
| **Who** assigns it? | **You** (TTN console) – can be any IEEE block you own |
| **What** is it? | Address of the **Join Server** that holds your AppKey |
| **Where** stored? | `credentials.h` – **same 8 bytes in every node firmware** |
| **When** used? | Sent in **Join-Request** – first packet after power-up |
| **Why** needed? | Routes request to correct TTN application & key vault |
| **How** added? | TTN console → Application settings → copy into `credentials.h` |

> Factory action: **hard-code once**, never changes per device.

---

## 3. Application Key – 0123456789ABCDEF0123456789ABCDEF  
*(AppKey – 128-bit AES secret)*

| Question | Answer |
|----------|--------|
| **Who** assigns it? | **You** (TTN console) – random 128-bit value |
| **What** is it? | Root AES-128 key shared **only** between node & TTN |
| **Where** stored? | `credentials.h` – **same 32 hex chars in every node** |
| **When** used? | **Never sent over air** – used to sign Join-Request MIC & derive session keys |
| **Why** needed? | Proves node identity & encrypts session keys during join |
| **How** protected? | **Back-up JSON immediately** – loss = bricks in field |

> Factory action: **copy once**, guard like SSL private key.

---

### Quick Sanity Check Before You Seal the Box
1. Barcode scanned = DevEUI in CSV ✔  
2. JoinEUI in hex = JoinEUI in TTN app ✔  
3. AppKey in hex = AppKey in TTN app ✔  

All three match → node will join.
import React from 'react';
import { Link, useLocation } from 'react-router-dom';
import './Sidebar.css';

const Sidebar = () => {
  const location = useLocation();

  const menuItems = [
    { path: '/', icon: '📊', label: 'Dashboard', count: 6 },
    { path: '/devices', icon: '📱', label: 'Device Management', count: 7 },
    { path: '/production', icon: '🏭', label: 'Production Workflow', count: 4 },
    { path: '/statistics', icon: '📈', label: 'Statistics', count: 3 }
  ];

  return (
    <aside className="sidebar">
      <div className="sidebar-content">
        <nav className="sidebar-nav">
          <div className="nav-section">
            <h3 className="nav-section-title">Main Navigation</h3>
            <ul className="nav-list">
              {menuItems.map((item, index) => (
                <li key={item.path} className="nav-item">
                  <Link 
                    to={item.path} 
                    className={`nav-link ${location.pathname === item.path ? 'active' : ''}`}
                  >
                    <span className="nav-icon">{item.icon}</span>
                    <span className="nav-label">{index + 1}. {item.label}</span>
                    <span className="nav-count">({item.count})</span>
                  </Link>
                </li>
              ))}
            </ul>
          </div>
          
          <div className="nav-section">
            <h3 className="nav-section-title">Quick Actions</h3>
            <ul className="nav-list">
              <li className="nav-item">
                <button className="nav-button btn-primary">
                  <span className="nav-icon">🚀</span>
                  <span className="nav-label">Start Production</span>
                </button>
              </li>
              <li className="nav-item">
                <button className="nav-button btn-secondary">
                  <span className="nav-icon">📋</span>
                  <span className="nav-label">View Reports</span>
                </button>
              </li>
            </ul>
          </div>
        </nav>
        
        <div className="sidebar-footer">
          <div className="system-info">
            <div className="info-item">
              <span className="info-label">Backend API:</span>
              <span className="info-value status-success">Connected</span>
            </div>
            <div className="info-item">
              <span className="info-label">Database:</span>
              <span className="info-value status-success">Online</span>
            </div>
            <div className="info-item">
              <span className="info-label">Total Endpoints:</span>
              <span className="info-value">26</span>
            </div>
          </div>
        </div>
      </div>
    </aside>
  );
};

export default Sidebar;

import React, { useState, useEffect } from 'react';
import axios from 'axios';

const Dashboard = () => {
  const [systemStatus, setSystemStatus] = useState(null);
  const [apiEndpoints, setApiEndpoints] = useState([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    fetchSystemStatus();
    fetchApiEndpoints();
  }, []);

  const fetchSystemStatus = async () => {
    try {
      const response = await axios.get('http://localhost:3000/api/status');
      setSystemStatus(response.data);
    } catch (error) {
      console.error('Failed to fetch system status:', error);
    }
  };

  const fetchApiEndpoints = async () => {
    try {
      // Simulate API endpoints data
      const endpoints = [
        { name: 'System Health', url: '/api/health', status: 'online' },
        { name: 'Database Test', url: '/api/database/test', status: 'online' },
        { name: 'Serial Generator', url: '/api/serial/test', status: 'online' },
        { name: 'Device Manager', url: '/api/devices/test', status: 'online' },
        { name: 'Validation System', url: '/api/validation/test', status: 'online' },
        { name: 'Migration System', url: '/api/migrations/test', status: 'online' }
      ];
      setApiEndpoints(endpoints);
      setLoading(false);
    } catch (error) {
      console.error('Failed to fetch API endpoints:', error);
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <div className="dashboard">
        <div className="card">
          <div className="card-title">Loading Dashboard...</div>
        </div>
      </div>
    );
  }

  return (
    <div className="dashboard">
      <div className="dashboard-header">
        <h1>📊 Production Dashboard</h1>
        <p>Real-time overview of the COLDRA Factory Tool system</p>
      </div>

      <div className="dashboard-grid">
        <div className="card">
          <div className="card-title">🏭 System Status</div>
          {systemStatus && (
            <div className="status-grid">
              <div className="status-item">
                <span className="status-label">System:</span>
                <span className="status-value status-success">{systemStatus.status}</span>
              </div>
              <div className="status-item">
                <span className="status-label">Version:</span>
                <span className="status-value">{systemStatus.version}</span>
              </div>
              <div className="status-item">
                <span className="status-label">Server:</span>
                <span className="status-value status-success">{systemStatus.components.server}</span>
              </div>
              <div className="status-item">
                <span className="status-label">Database:</span>
                <span className="status-value status-warning">not_configured</span>
              </div>
            </div>
          )}
        </div>

        <div className="card">
          <div className="card-title">📈 Production Statistics</div>
          <div className="stats-grid">
            <div className="stat-item">
              <div className="stat-number">0</div>
              <div className="stat-label">Devices Today</div>
            </div>
            <div className="stat-item">
              <div className="stat-number">0</div>
              <div className="stat-label">Total Devices</div>
            </div>
            <div className="stat-item">
              <div className="stat-number">0%</div>
              <div className="stat-label">Yield Rate</div>
            </div>
          </div>
        </div>

        <div className="card">
          <div className="card-title">🔧 API Endpoints Status</div>
          <div className="endpoints-list">
            {apiEndpoints.map((endpoint, index) => (
              <div key={index} className="endpoint-item">
                <span className="endpoint-number">{index + 1}.</span>
                <span className="endpoint-name">{endpoint.name}</span>
                <span className={`endpoint-status status-${endpoint.status === 'online' ? 'success' : 'error'}`}>
                  {endpoint.status}
                </span>
              </div>
            ))}
          </div>
        </div>

        <div className="card">
          <div className="card-title">🚀 Quick Actions</div>
          <div className="actions-grid">
            <button className="btn btn-primary">Start Production</button>
            <button className="btn btn-primary">Create Device</button>
            <button className="btn btn-primary">View Statistics</button>
            <button className="btn btn-primary">Generate Report</button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Dashboard;

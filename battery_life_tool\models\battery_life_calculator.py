"""
Battery Life Calculator - Integrated Calculation Engine
======================================================

This module integrates all component models to calculate realistic battery life
for the COLDRA LoRaWAN temperature sensor.

Components:
- Energizer L91 Battery (2x AA in series)
- RAK3172 LoRaWAN Module  
- MAX31820 Temperature Sensor
- Green LED indicator
"""

import logging
from typing import Dict, Tuple, Optional
from .battery_model import EnergizeL91BatteryModel
from .rak3172_model import RAK3172PowerModel
from .max31820_model import MAX31820SensorModel
from .configuration_manager import ConfigurationManager

logger = logging.getLogger(__name__)


class BatteryLifeCalculator:
    """
    Integrated battery life calculator for COLDRA temperature sensor.

    Combines all component models to provide accurate battery life predictions
    with detailed energy breakdown.

    NOW USES CONFIGURABLE PARAMETERS that should be measured by firmware team
    instead of hardcoded datasheet values.
    """

    def __init__(self, config_manager: ConfigurationManager = None):
        """Initialize the integrated calculator with all component models and configuration."""

        # Initialize configuration manager
        self.config_manager = config_manager or ConfigurationManager()

        # Initialize component models with configuration
        self.battery = EnergizeL91BatteryModel()
        self.lora_module = RAK3172PowerModel(self.config_manager)
        self.temp_sensor = MAX31820SensorModel(self.config_manager)
        
        # LED parameters (estimated)
        self.led_current_ma = 5.0    # mA during blink
        self.led_blink_time_ms = 100  # ms per blink
        
        logger.info("Battery Life Calculator initialized")

    def update_configuration(self, config_manager: ConfigurationManager):
        """Update configuration and reinitialize models"""
        self.config_manager = config_manager

        # Update component models with new configuration
        self.lora_module.update_configuration(config_manager)
        if hasattr(self.temp_sensor, 'update_configuration'):
            self.temp_sensor.update_configuration(config_manager)

        logger.info("Battery Life Calculator configuration updated")

    def calculate_battery_life(self,
                             # Environmental parameters
                             temperature: float = 20.0,
                             
                             # LoRaWAN parameters
                             tx_power_dbm: float = 14,
                             tx_interval_minutes: int = 15,
                             spreading_factor: str = 'SF7',
                             sleep_mode: str = 'stop2',
                             
                             # Sensor parameters
                             sensor_resolution_bits: int = 12,
                             measurements_per_tx: int = 1,
                             
                             # LED parameters
                             led_enabled: bool = True,
                             
                             # Battery parameters
                             battery_count: int = 2) -> Tuple[float, Dict[str, any]]:
        """
        Calculate complete battery life with detailed breakdown.
        
        Args:
            temperature: Operating temperature in Celsius
            tx_power_dbm: LoRaWAN TX power level (7-22 dBm)
            tx_interval_minutes: Transmission interval in minutes
            spreading_factor: LoRaWAN spreading factor (SF7-SF12)
            sleep_mode: Sleep mode between transmissions
            sensor_resolution_bits: Temperature sensor resolution (9-12 bits)
            measurements_per_tx: Sensor measurements per transmission
            led_enabled: Whether LED blink is enabled
            battery_count: Number of AA batteries (default: 2)
            
        Returns:
            Tuple of (battery_life_days, detailed_breakdown)
        """
        breakdown = {
            'input_parameters': {
                'temperature_C': temperature,
                'tx_power_dbm': tx_power_dbm,
                'tx_interval_minutes': tx_interval_minutes,
                'spreading_factor': spreading_factor,
                'sleep_mode': sleep_mode,
                'sensor_resolution_bits': sensor_resolution_bits,
                'measurements_per_tx': measurements_per_tx,
                'led_enabled': led_enabled,
                'battery_count': battery_count
            }
        }
        
        # Calculate transmissions per day
        tx_per_day = (24 * 60) / tx_interval_minutes
        sensor_measurements_per_day = tx_per_day * measurements_per_tx
        
        # 1. LoRaWAN Module Energy
        lora_daily_energy, lora_breakdown = self.lora_module.calculate_daily_energy_mah(
            tx_power_dbm=tx_power_dbm,
            tx_interval_minutes=tx_interval_minutes,
            spreading_factor=spreading_factor,
            sleep_mode=sleep_mode,
            sensor_measurements_per_tx=measurements_per_tx,
            temperature=temperature
        )
        breakdown['lora_module'] = lora_breakdown
        
        # 2. Temperature Sensor Energy
        sensor_daily_energy, sensor_breakdown = self.temp_sensor.calculate_daily_energy_mah(
            measurements_per_day=sensor_measurements_per_day,
            resolution_bits=sensor_resolution_bits,
            temperature=temperature
        )
        breakdown['temp_sensor'] = sensor_breakdown
        
        # 3. LED Energy (if enabled)
        led_daily_energy = 0.0
        if led_enabled:
            led_energy_per_blink = (self.led_current_ma * self.led_blink_time_ms) / (1000 * 3600)  # mAh
            led_daily_energy = led_energy_per_blink * tx_per_day
        
        breakdown['led'] = {
            'enabled': led_enabled,
            'current_mA': self.led_current_ma if led_enabled else 0,
            'blink_time_ms': self.led_blink_time_ms if led_enabled else 0,
            'blinks_per_day': tx_per_day if led_enabled else 0,
            'daily_energy_mAh': led_daily_energy
        }
        
        # 4. Total Daily Energy Consumption
        total_daily_energy = lora_daily_energy + sensor_daily_energy + led_daily_energy
        
        breakdown['energy_summary'] = {
            'lora_daily_energy_mAh': lora_daily_energy,
            'sensor_daily_energy_mAh': sensor_daily_energy,
            'led_daily_energy_mAh': led_daily_energy,
            'total_daily_energy_mAh': total_daily_energy
        }
        
        # Energy distribution percentages
        if total_daily_energy > 0:
            breakdown['energy_summary']['lora_energy_percent'] = (lora_daily_energy / total_daily_energy) * 100
            breakdown['energy_summary']['sensor_energy_percent'] = (sensor_daily_energy / total_daily_energy) * 100
            breakdown['energy_summary']['led_energy_percent'] = (led_daily_energy / total_daily_energy) * 100
        
        # 5. Battery Capacity Calculation
        # Calculate average current for battery capacity estimation
        average_current_ma = total_daily_energy / 24  # mAh/day ÷ 24h = mA
        
        # Estimate peak current (during TX)
        peak_current_ma = lora_breakdown['tx_current_mA'] + sensor_breakdown['active_current_mA']
        if led_enabled:
            peak_current_ma += self.led_current_ma
        
        # Calculate duty cycle for peak current
        tx_time_per_day_hours = (lora_breakdown['airtime_ms'] * tx_per_day) / (1000 * 3600)
        sensor_time_per_day_hours = (sensor_breakdown['conversion_time_ms'] * sensor_measurements_per_day) / (1000 * 3600)
        peak_time_per_day_hours = tx_time_per_day_hours + sensor_time_per_day_hours
        duty_cycle = peak_time_per_day_hours / 24
        
        # Get effective battery capacity
        effective_capacity, battery_breakdown = self.battery.get_effective_capacity(
            temperature=temperature,
            average_current=average_current_ma,
            peak_current=peak_current_ma,
            duty_cycle=duty_cycle
        )
        
        # Adjust for multiple batteries
        total_battery_capacity = effective_capacity * battery_count
        
        breakdown['battery'] = battery_breakdown
        breakdown['battery']['battery_count'] = battery_count
        breakdown['battery']['total_capacity_mAh'] = total_battery_capacity
        breakdown['battery']['average_current_mA'] = average_current_ma
        breakdown['battery']['peak_current_mA'] = peak_current_ma
        breakdown['battery']['duty_cycle'] = duty_cycle
        
        # 6. Battery Life Calculation
        if total_daily_energy > 0:
            battery_life_days = total_battery_capacity / total_daily_energy
        else:
            battery_life_days = float('inf')
        
        breakdown['battery_life'] = {
            'days': battery_life_days,
            'weeks': battery_life_days / 7,
            'months': battery_life_days / 30.44,  # Average month length
            'years': battery_life_days / 365.25
        }
        
        # 7. Confidence Assessment
        confidence_factors = {
            'battery_data': 0.95,      # High confidence in L91 datasheet data
            'lora_tx_data': 0.90,      # Good confidence in RAK3172 TX data
            'lora_sleep_data': 0.85,   # Good confidence in sleep data
            'sensor_data': 0.95,       # High confidence in MAX31820 data
            'temperature_model': 0.80, # Good confidence in temperature effects
            'integration_model': 0.75  # Moderate confidence in integration
        }
        
        overall_confidence = min(confidence_factors.values())
        breakdown['confidence'] = {
            'factors': confidence_factors,
            'overall': overall_confidence,
            'level': 'High' if overall_confidence >= 0.85 else 'Good' if overall_confidence >= 0.75 else 'Moderate'
        }
        
        return battery_life_days, breakdown
    
    def compare_scenarios(self, scenarios: Dict[str, Dict]) -> Dict[str, Dict]:
        """
        Compare multiple scenarios and return results.
        
        Args:
            scenarios: Dictionary of scenario_name -> parameters
            
        Returns:
            Dictionary of scenario_name -> results
        """
        results = {}
        
        for scenario_name, params in scenarios.items():
            try:
                battery_life_days, breakdown = self.calculate_battery_life(**params)
                results[scenario_name] = {
                    'battery_life_days': battery_life_days,
                    'battery_life_years': battery_life_days / 365.25,
                    'daily_energy_mAh': breakdown['energy_summary']['total_daily_energy_mAh'],
                    'confidence': breakdown['confidence']['overall'],
                    'breakdown': breakdown
                }
            except Exception as e:
                logger.error(f"Error calculating scenario '{scenario_name}': {e}")
                results[scenario_name] = {'error': str(e)}
        
        return results


def test_battery_life_calculator():
    """Test the integrated battery life calculator with realistic scenarios."""
    calculator = BatteryLifeCalculator()
    
    print("=== Battery Life Calculator Test ===")
    
    # Define test scenarios
    scenarios = {
        'Typical Freezer': {
            'temperature': -20.0,
            'tx_power_dbm': 14,
            'tx_interval_minutes': 15,
            'spreading_factor': 'SF7',
            'sleep_mode': 'stop2'
        },
        'Room Temperature': {
            'temperature': 20.0,
            'tx_power_dbm': 10,
            'tx_interval_minutes': 30,
            'spreading_factor': 'SF7',
            'sleep_mode': 'stop2'
        },
        'Extreme Cold': {
            'temperature': -40.0,
            'tx_power_dbm': 20,
            'tx_interval_minutes': 10,
            'spreading_factor': 'SF12',
            'sleep_mode': 'stop2'
        },
        'Power Saving': {
            'temperature': 0.0,
            'tx_power_dbm': 7,
            'tx_interval_minutes': 60,
            'spreading_factor': 'SF7',
            'sleep_mode': 'stop2',
            'sensor_resolution_bits': 9,
            'led_enabled': False
        }
    }
    
    # Calculate and display results
    results = calculator.compare_scenarios(scenarios)
    
    for scenario_name, result in results.items():
        if 'error' in result:
            print(f"\n{scenario_name}: ERROR - {result['error']}")
            continue
            
        print(f"\n{scenario_name}:")
        print(f"  Battery Life: {result['battery_life_years']:.2f} years ({result['battery_life_days']:.0f} days)")
        print(f"  Daily Energy: {result['daily_energy_mAh']:.3f} mAh")
        print(f"  Confidence: {result['confidence']:.0%}")
        
        # Energy breakdown
        breakdown = result['breakdown']['energy_summary']
        print(f"  Energy Distribution:")
        print(f"    - LoRa: {breakdown['lora_energy_percent']:.1f}%")
        print(f"    - Sensor: {breakdown['sensor_energy_percent']:.1f}%")
        print(f"    - LED: {breakdown['led_energy_percent']:.1f}%")


if __name__ == "__main__":
    test_battery_life_calculator()

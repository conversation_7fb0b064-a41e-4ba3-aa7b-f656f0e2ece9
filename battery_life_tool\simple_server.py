#!/usr/bin/env python3
"""
Simple FastAPI server for battery life calculations
"""

import sys
import os

# Add current directory to path
sys.path.insert(0, '.')

from fastapi import FastAPI, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel
from typing import Dict, Any
from models.battery_life_calculator import BatteryLifeCalculator
from models.configuration_manager import ConfigurationManager
import uvicorn

print("Initializing COLDRA Battery Life API...")

# Create FastAPI app
app = FastAPI(
    title="COLDRA Battery Life API",
    description="Battery life calculator for LoRaWAN temperature sensors",
    version="1.0.0"
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["http://localhost:3000", "http://localhost:3001", "http://127.0.0.1:3000"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Initialize configuration manager and calculator
try:
    config_manager = ConfigurationManager()
    calculator = BatteryLifeCalculator(config_manager)
    print("✅ Calculator initialized successfully with configuration manager")
except Exception as e:
    print(f"❌ Calculator initialization failed: {e}")
    raise

# Request model
class CalculationRequest(BaseModel):
    temperature: float = 20.0
    tx_power_dbm: float = 14
    tx_interval_minutes: int = 15
    spreading_factor: str = "SF7"

# Routes
@app.get("/")
def root():
    return {
        "message": "COLDRA Battery Life Assessment API",
        "status": "running",
        "version": "1.0.0"
    }

@app.get("/health")
def health():
    return {
        "status": "healthy",
        "calculator": "initialized"
    }

@app.post("/calculate-battery-life")
def calculate_battery_life(request: CalculationRequest):
    try:
        print(f"Calculating for: {request.dict()}")
        
        # Perform calculation
        battery_life_days, breakdown = calculator.calculate_battery_life(
            temperature=request.temperature,
            tx_power_dbm=request.tx_power_dbm,
            tx_interval_minutes=request.tx_interval_minutes,
            spreading_factor=request.spreading_factor,
            sleep_mode="stop2",
            sensor_resolution_bits=12,
            measurements_per_tx=1,
            led_enabled=True,
            battery_count=2
        )
        
        # Format response with detailed breakdown
        response = {
            "battery_life_years": round(battery_life_days / 365.25, 2),
            "battery_life_days": round(battery_life_days, 1),
            "daily_energy_mah": round(breakdown['energy_summary']['total_daily_energy_mAh'], 6),
            "energy_breakdown": {
                "lora_percent": round(breakdown['energy_summary'].get('lora_energy_percent', 0), 1),
                "sensor_percent": round(breakdown['energy_summary'].get('sensor_energy_percent', 0), 1),
                "led_percent": round(breakdown['energy_summary'].get('led_energy_percent', 0), 1)
            },
            "battery_info": {
                "effective_capacity_mah": round(breakdown['battery']['effective_capacity_mAh'], 0),
                "average_current_ma": round(breakdown['battery']['average_current_mA'], 6)
            },
            "lora_details": {
                "tx_current_ma": round(breakdown['lora_module']['tx_current_mA'], 3),
                "sleep_current_ua": round(breakdown['lora_module']['sleep_current_uA'], 3),
                "tx_time_ms": round(breakdown['lora_module']['airtime_ms'], 1),
                "sleep_time_hours": round(breakdown['lora_module']['sleep_time_hours'], 3),
                "tx_per_day": int(breakdown['lora_module']['tx_per_day'])
            },
            "sensor_details": {
                "active_current_ma": round(breakdown['temp_sensor']['active_current_mA'], 3),
                "standby_current_ua": round(breakdown['temp_sensor']['standby_current_uA'], 3),
                "conversion_time_ms": round(breakdown['temp_sensor']['conversion_time_ms'], 1),
                "standby_time_hours": round(breakdown['temp_sensor']['standby_time_hours'], 3),
                "measurements_per_day": int(breakdown['temp_sensor']['measurements_per_day'])
            },
            "confidence_level": breakdown['confidence']['overall'],
            "confidence_description": breakdown['confidence']['level'],
            "input_parameters": request.dict()
        }
        
        print(f"Result: {response['battery_life_years']} years")
        return response
        
    except Exception as e:
        print(f"Calculation error: {e}")
        raise HTTPException(status_code=500, detail=f"Calculation failed: {str(e)}")


@app.get("/get-configuration")
async def get_configuration():
    """Get current configuration parameters"""
    try:
        config_data = config_manager.get_all_configs()
        return {
            "success": True,
            "configuration": config_data,
            "message": "Configuration retrieved successfully"
        }
    except Exception as e:
        print(f"Configuration retrieval error: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to get configuration: {str(e)}")


@app.post("/save-configuration")
async def save_configuration(config_updates: Dict[str, Any]):
    """Save configuration parameters and reinitialize calculator"""
    try:
        # Update each configuration section
        for config_type, updates in config_updates.items():
            if config_type in ['lora_config', 'sensor_config', 'system_config', 'battery_config', 'firmware_config', 'environmental_config']:
                success = config_manager.update_config(config_type, updates)
                if not success:
                    raise HTTPException(status_code=400, detail=f"Failed to update {config_type}")

        # Save configuration to file
        save_success = config_manager.save_configuration()
        if not save_success:
            raise HTTPException(status_code=500, detail="Failed to save configuration to file")

        # Update calculator with new configuration
        global calculator
        calculator.update_configuration(config_manager)

        return {
            "success": True,
            "message": "Configuration saved successfully and calculator reinitialized",
            "configuration": config_manager.get_all_configs()
        }

    except Exception as e:
        print(f"Configuration save error: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to save configuration: {str(e)}")


@app.post("/reset-configuration")
async def reset_configuration():
    """Reset configuration to defaults"""
    try:
        # Create new configuration manager with defaults
        global config_manager, calculator
        config_manager = ConfigurationManager()
        calculator = BatteryLifeCalculator(config_manager)

        # Save default configuration
        config_manager.save_configuration()

        return {
            "success": True,
            "message": "Configuration reset to defaults",
            "configuration": config_manager.get_all_configs()
        }

    except Exception as e:
        print(f"Configuration reset error: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to reset configuration: {str(e)}")


if __name__ == "__main__":
    print("Starting COLDRA Battery Life API server...")
    print("Server will be available at: http://localhost:8001")
    print("API documentation at: http://localhost:8001/docs")

    uvicorn.run(
        app,
        host="0.0.0.0",
        port=8001,
        log_level="info"
    )

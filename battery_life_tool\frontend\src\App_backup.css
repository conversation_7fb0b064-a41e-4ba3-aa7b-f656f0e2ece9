/* COLDRA Battery Life Assessment Tool Styles */

.App {
  text-align: center;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: #333;
}

.App-header {
  background: rgba(255, 255, 255, 0.95);
  padding: 2rem;
  color: #333;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.App-header h1 {
  margin: 0 0 0.5rem 0;
  font-size: 2.5rem;
  font-weight: 700;
  color: #2c3e50;
}

.App-header p {
  margin: 0;
  font-size: 1.2rem;
  color: #7f8c8d;
}

.App-main {
  flex: 1;
  padding: 2rem;
  display: flex;
  justify-content: center;
  align-items: flex-start;
}

.calculator-container {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 3rem;
  max-width: 1200px;
  width: 100%;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 20px;
  padding: 2rem;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
}

.input-section, .results-section {
  padding: 1rem;
}

.input-section h2, .results-section h2 {
  color: #2c3e50;
  margin-bottom: 1.5rem;
  font-size: 1.8rem;
  font-weight: 600;
}

.input-group {
  margin-bottom: 2rem;
  text-align: left;
}

.input-group label {
  display: block;
  margin-bottom: 0.5rem;
  font-weight: 600;
  color: #34495e;
  font-size: 1.1rem;
}

.slider {
  width: 100%;
  height: 8px;
  border-radius: 5px;
  background: #ddd;
  outline: none;
  -webkit-appearance: none;
  margin-bottom: 0.5rem;
}

.slider::-webkit-slider-thumb {
  -webkit-appearance: none;
  appearance: none;
  width: 20px;
  height: 20px;
  border-radius: 50%;
  background: #667eea;
  cursor: pointer;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.2);
}

.slider::-moz-range-thumb {
  width: 20px;
  height: 20px;
  border-radius: 50%;
  background: #667eea;
  cursor: pointer;
  border: none;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.2);
}

.range-labels {
  display: flex;
  justify-content: space-between;
  font-size: 0.9rem;
  color: #7f8c8d;
}

.select, .number-input {
  width: 100%;
  padding: 0.75rem;
  border: 2px solid #ddd;
  border-radius: 8px;
  font-size: 1rem;
  background: white;
  transition: border-color 0.3s ease;
}

.select:focus, .number-input:focus {
  outline: none;
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.results {
  text-align: center;
}

.main-result {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 2rem;
  border-radius: 15px;
  margin-bottom: 2rem;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
}

.main-result h3 {
  margin: 0 0 0.5rem 0;
  font-size: 3rem;
  font-weight: 700;
}

.main-result p {
  margin: 0;
  font-size: 1.2rem;
  opacity: 0.9;
}

.details {
  text-align: left;
}

.detail-item {
  display: flex;
  justify-content: space-between;
  padding: 0.75rem 0;
  border-bottom: 1px solid #eee;
  font-size: 1rem;
}

.detail-item:last-child {
  border-bottom: none;
}

.energy-breakdown, .battery-info, .lora-details, .sensor-details {
  margin-top: 1.5rem;
  padding: 1rem;
  background: #f8f9fa;
  border-radius: 10px;
}

.energy-breakdown h4, .battery-info h4, .lora-details h4, .sensor-details h4 {
  margin: 0 0 1rem 0;
  color: #2c3e50;
  font-size: 1.2rem;
}

.lora-details {
  background: #e8f4fd;
  border-left: 4px solid #667eea;
}

.sensor-details {
  background: #f0f8e8;
  border-left: 4px solid #27ae60;
}

.breakdown-item {
  display: flex;
  justify-content: space-between;
  padding: 0.5rem 0;
  font-size: 0.95rem;
}

.loading {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
  color: #667eea;
  font-size: 1.2rem;
}

.error {
  background: #e74c3c;
  color: white;
  padding: 1rem;
  border-radius: 8px;
  margin: 1rem 0;
}

.App-footer {
  background: rgba(255, 255, 255, 0.95);
  padding: 1.5rem;
  color: #7f8c8d;
  font-size: 0.9rem;
  box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.1);
}

.App-footer p {
  margin: 0.25rem 0;
}

/* Responsive Design */
@media (max-width: 768px) {
  .calculator-container {
    grid-template-columns: 1fr;
    gap: 2rem;
    padding: 1rem;
  }

  .App-header h1 {
    font-size: 2rem;
  }

  .main-result h3 {
    font-size: 2.5rem;
  }

  .config-modal {
    width: 95%;
    margin: 1rem;
  }

  .config-tabs {
    flex-wrap: wrap;
  }

  .config-grid {
    grid-template-columns: 1fr;
    gap: 0.5rem;
  }
}

/* Header buttons */
.header-buttons {
  margin-top: 1rem;
}

.config-button {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
  color: white;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 25px;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 4px 15px rgba(240, 147, 251, 0.3);
}

.config-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(240, 147, 251, 0.4);
}

/* Configuration Modal */
.config-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.7);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
  padding: 2rem;
}

.config-modal {
  background: white;
  border-radius: 15px;
  width: 90%;
  max-width: 800px;
  max-height: 90vh;
  overflow-y: auto;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
}

.config-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 1.5rem;
  border-radius: 15px 15px 0 0;
  position: relative;
}

.config-header h2 {
  margin: 0 0 0.5rem 0;
  font-size: 1.5rem;
}

.config-warning {
  background: rgba(255, 255, 255, 0.2);
  padding: 0.75rem;
  border-radius: 8px;
  margin: 0.5rem 0 0 0;
  font-size: 0.9rem;
  border-left: 4px solid #ffd700;
}

.close-button {
  position: absolute;
  top: 1rem;
  right: 1rem;
  background: rgba(255, 255, 255, 0.2);
  color: white;
  border: none;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  font-size: 1.2rem;
  cursor: pointer;
  transition: all 0.3s ease;
}

.close-button:hover {
  background: rgba(255, 255, 255, 0.3);
  transform: scale(1.1);
}

/* Configuration Tabs */
.config-tabs {
  display: flex;
  background: #f8f9fa;
  border-bottom: 1px solid #dee2e6;
  overflow-x: auto;
}

.tab-button {
  background: none;
  border: none;
  padding: 1rem 1.5rem;
  cursor: pointer;
  font-size: 0.9rem;
  font-weight: 500;
  color: #6c757d;
  transition: all 0.3s ease;
  white-space: nowrap;
  border-bottom: 3px solid transparent;
}

.tab-button:hover {
  background: #e9ecef;
  color: #495057;
}

.tab-button.active {
  color: #667eea;
  border-bottom-color: #667eea;
  background: white;
}

/* Configuration Content */
.config-content {
  padding: 2rem;
  min-height: 400px;
}

.config-section h3 {
  color: #2c3e50;
  margin: 0 0 1rem 0;
  font-size: 1.3rem;
}

.section-note {
  background: #fff3cd;
  border: 1px solid #ffeaa7;
  border-left: 4px solid #f39c12;
  padding: 0.75rem;
  border-radius: 5px;
  margin-bottom: 1.5rem;
  font-size: 0.9rem;
}

.config-group {
  margin-bottom: 2rem;
  padding: 1rem;
  background: #f8f9fa;
  border-radius: 8px;
  border-left: 4px solid #667eea;
}

.config-group h4 {
  color: #495057;
  margin: 0 0 1rem 0;
  font-size: 1.1rem;
}

.config-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1rem;
  align-items: center;
}

.config-grid label {
  font-weight: 500;
  color: #495057;
}

.config-grid input[type="number"] {
  padding: 0.5rem;
  border: 1px solid #ced4da;
  border-radius: 5px;
  font-size: 0.9rem;
}

.config-grid input[type="checkbox"] {
  width: 20px;
  height: 20px;
}

/**
 * Configuration Manager - COLDRA Factory Tool
 * Handles JSON configuration loading and validation
 */

const fs = require('fs');
const path = require('path');

class ConfigManager {
    constructor() {
        this.configs = new Map();
        this.configDir = path.join(__dirname, '../../config');
    }

    // Load and validate TTN credentials
    loadTTNCredentials(filePath) {
        try {
            const fullPath = path.resolve(filePath);
            const data = fs.readFileSync(fullPath, 'utf8');
            const config = JSON.parse(data);
            
            // Validate TTN config structure
            this.validateTTNConfig(config);
            
            this.configs.set('ttn', config);
            return { success: true, config };
        } catch (error) {
            return { success: false, error: error.message };
        }
    }

    // Validate TTN configuration
    validateTTNConfig(config) {
        const required = ['application_name', 'join_eui', 'app_key', 'region'];
        
        for (const field of required) {
            if (!config[field]) {
                throw new Error(`Missing required field: ${field}`);
            }
        }

        // Validate hex formats
        if (!/^[0-9A-Fa-f]{16}$/.test(config.join_eui)) {
            throw new Error('join_eui must be 16 hex characters');
        }
        
        if (!/^[0-9A-Fa-f]{32}$/.test(config.app_key)) {
            throw new Error('app_key must be 32 hex characters');
        }

        // Validate region
        const validRegions = ['US915', 'EU868', 'IN865'];
        if (!validRegions.includes(config.region)) {
            throw new Error(`Invalid region. Must be one of: ${validRegions.join(', ')}`);
        }
    }

    // Get configuration
    getConfig(type) {
        return this.configs.get(type);
    }

    // Create sample TTN config
    createSampleTTNConfig() {
        const sample = {
            application_name: "COLDRA_Production",
            join_eui: "FEDCBA0987654321",
            app_key: "00112233445566778899AABBCCDDEEFF",
            region: "US915",
            created_date: new Date().toISOString()
        };
        
        const filePath = path.join(this.configDir, 'sample_ttn_credentials.json');
        fs.writeFileSync(filePath, JSON.stringify(sample, null, 2));
        return filePath;
    }
}

module.exports = ConfigManager;

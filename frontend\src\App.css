/* UST Branding Colors */
:root {
  --ust-dark-teal: #006E74;
  --ust-light-teal: #0097AC;
  --ust-white: #FFFFFF;
  --ust-soft-black: #231F20;
  --ust-gray: #f5f5f5;
  --ust-border: #e0e0e0;
}

/* Global Styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  background-color: var(--ust-gray);
  color: var(--ust-soft-black);
}

.App {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

.app-container {
  display: flex;
  flex: 1;
}

.main-content {
  flex: 1;
  padding: 20px;
  background-color: var(--ust-white);
  margin-left: 250px;
  min-height: calc(100vh - 60px);
}

@media (max-width: 768px) {
  .main-content {
    margin-left: 0;
    padding: 10px;
  }
}

.btn {
  padding: 10px 20px;
  border: none;
  border-radius: 5px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.3s ease;
}

.btn-primary {
  background-color: var(--ust-light-teal);
  color: var(--ust-white);
}

.btn-primary:hover {
  background-color: var(--ust-dark-teal);
}

.card {
  background-color: var(--ust-white);
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  padding: 20px;
  margin-bottom: 20px;
}

.card-title {
  color: var(--ust-dark-teal);
  font-size: 18px;
  font-weight: 600;
  margin-bottom: 15px;
}

/* Status Indicators */
.status-success {
  color: #28a745;
  font-weight: 600;
}

.status-error {
  color: #dc3545;
  font-weight: 600;
}

.status-warning {
  color: #ffc107;
  font-weight: 600;
}

.status-info {
  color: var(--ust-light-teal);
  font-weight: 600;
}

/* Page Layouts */
.page-header {
  margin-bottom: 30px;
}

.page-header h1 {
  color: var(--ust-dark-teal);
  font-size: 28px;
  font-weight: 600;
  margin-bottom: 10px;
}

.page-header p {
  color: var(--ust-soft-black);
  font-size: 16px;
  opacity: 0.8;
}

.page-actions {
  margin-bottom: 20px;
}

/* Dashboard Styles */
.dashboard-header {
  text-align: center;
  margin-bottom: 30px;
}

.dashboard-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 20px;
}

.status-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 15px;
}

.status-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px;
  background-color: var(--ust-gray);
  border-radius: 5px;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 20px;
  text-align: center;
}

.stat-item {
  padding: 15px;
}

.stat-number {
  font-size: 24px;
  font-weight: 600;
  color: var(--ust-light-teal);
  margin-bottom: 5px;
}

.stat-label {
  font-size: 12px;
  color: var(--ust-soft-black);
  text-transform: uppercase;
}

.endpoints-list {
  space-y: 10px;
}

.endpoint-item {
  display: flex;
  align-items: center;
  padding: 10px;
  background-color: var(--ust-gray);
  border-radius: 5px;
  margin-bottom: 8px;
}

.endpoint-number {
  font-weight: 600;
  color: var(--ust-light-teal);
  margin-right: 10px;
  min-width: 25px;
}

.endpoint-name {
  flex: 1;
  font-weight: 500;
  margin-right: 10px;
}

.endpoint-status {
  font-size: 12px;
  text-transform: uppercase;
}

.actions-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 10px;
}

/* Form Styles */
.form-group {
  margin-bottom: 15px;
}

.form-label {
  display: block;
  margin-bottom: 5px;
  color: var(--ust-soft-black);
  font-weight: 500;
}

.form-input {
  width: 100%;
  padding: 10px;
  border: 2px solid var(--ust-border);
  border-radius: 5px;
  font-size: 14px;
}

.form-input:focus {
  outline: none;
  border-color: var(--ust-light-teal);
}

.form-row {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 20px;
}

.form-actions {
  display: flex;
  gap: 10px;
  margin-top: 20px;
}

.btn-secondary {
  background-color: var(--ust-gray);
  color: var(--ust-soft-black);
  border: 1px solid var(--ust-border);
}

.btn-secondary:hover {
  background-color: var(--ust-border);
}

/* Empty State */
.empty-state {
  text-align: center;
  padding: 40px 20px;
  color: var(--ust-soft-black);
  opacity: 0.7;
}

/* Loading State */
.loading {
  text-align: center;
  padding: 20px;
  color: var(--ust-light-teal);
}

/* Workflow Styles */
.workflow-progress {
  margin-bottom: 30px;
}

.progress-steps {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 20px;
  padding: 20px;
  background-color: var(--ust-white);
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.progress-step {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  position: relative;
}

.progress-step.current .step-icon {
  background-color: var(--ust-light-teal);
  color: var(--ust-white);
}

.progress-step.pending .step-icon {
  background-color: var(--ust-gray);
  color: var(--ust-soft-black);
}

.step-icon {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16px;
  margin-bottom: 8px;
}

.step-name {
  font-size: 12px;
  font-weight: 500;
  color: var(--ust-soft-black);
}

.step-connector {
  position: absolute;
  top: 20px;
  left: 50px;
  width: 40px;
  height: 2px;
  background-color: var(--ust-border);
}

/* Alert Styles */
.alert {
  padding: 12px 16px;
  border-radius: 5px;
  margin-bottom: 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.alert-error {
  background-color: #f8d7da;
  color: #721c24;
  border: 1px solid #f5c6cb;
}

.alert-success {
  background-color: #d4edda;
  color: #155724;
  border: 1px solid #c3e6cb;
}

.alert-close {
  background: none;
  border: none;
  font-size: 18px;
  cursor: pointer;
  color: inherit;
  padding: 0;
  margin-left: 10px;
}

/* Search Box */
.search-box {
  flex: 1;
  max-width: 400px;
  margin-left: 20px;
}

.page-actions {
  display: flex;
  align-items: center;
  margin-bottom: 20px;
}

/* Form Help Text */
.form-help {
  display: block;
  font-size: 12px;
  color: var(--ust-soft-black);
  opacity: 0.7;
  margin-top: 4px;
}

/* Device Table */
.devices-table {
  overflow-x: auto;
}

.table-header {
  background-color: var(--ust-gray);
  border-radius: 5px 5px 0 0;
}

.table-row {
  display: grid;
  grid-template-columns: 1.5fr 2fr 1fr 1fr 1fr 1fr;
  gap: 15px;
  padding: 12px 15px;
  border-bottom: 1px solid var(--ust-border);
  align-items: center;
}

.header-row {
  font-weight: 600;
  color: var(--ust-dark-teal);
  background-color: var(--ust-gray);
}

.table-cell {
  padding: 5px 0;
}

.table-cell code {
  background-color: var(--ust-gray);
  padding: 2px 6px;
  border-radius: 3px;
  font-family: 'Courier New', monospace;
  font-size: 12px;
}

/* Status and Region Badges */
.region-badge, .status-badge {
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 11px;
  font-weight: 600;
  text-transform: uppercase;
}

.region-us915 {
  background-color: #e3f2fd;
  color: #1976d2;
}

.region-eu868 {
  background-color: #f3e5f5;
  color: #7b1fa2;
}

.region-in865 {
  background-color: #fff3e0;
  color: #f57c00;
}

.status-created {
  background-color: var(--ust-gray);
  color: var(--ust-soft-black);
}

.status-programmed {
  background-color: #e8f5e8;
  color: #2e7d32;
}

.status-provisioned {
  background-color: #e3f2fd;
  color: #1976d2;
}

.status-tested {
  background-color: #f3e5f5;
  color: #7b1fa2;
}

/* Action Buttons */
.action-buttons {
  display: flex;
  gap: 8px;
}

.btn-action {
  width: 32px;
  height: 32px;
  border: none;
  border-radius: 5px;
  cursor: pointer;
  font-size: 14px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.btn-edit {
  background-color: #fff3cd;
  color: #856404;
}

.btn-edit:hover {
  background-color: #ffeaa7;
}

.btn-delete {
  background-color: #f8d7da;
  color: #721c24;
}

.btn-delete:hover {
  background-color: #f5c6cb;
}

/* Modal Styles */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 2000;
}

.modal {
  background-color: var(--ust-white);
  border-radius: 8px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
  max-width: 600px;
  width: 90%;
  max-height: 90vh;
  overflow-y: auto;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px;
  border-bottom: 1px solid var(--ust-border);
}

.modal-header h3 {
  color: var(--ust-dark-teal);
  margin: 0;
}

.modal-close {
  background: none;
  border: none;
  font-size: 24px;
  cursor: pointer;
  color: var(--ust-soft-black);
  padding: 0;
  width: 30px;
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
}

.modal-close:hover {
  background-color: var(--ust-gray);
}

.modal .device-form {
  padding: 20px;
}

/* Responsive Design for Tables */
@media (max-width: 768px) {
  .table-row {
    grid-template-columns: 1fr;
    gap: 8px;
  }

  .table-cell {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .table-cell::before {
    content: attr(data-label);
    font-weight: 600;
    color: var(--ust-dark-teal);
  }

  .header-row {
    display: none;
  }

  .search-box {
    margin-left: 0;
    margin-top: 10px;
  }

  .page-actions {
    flex-direction: column;
    align-items: stretch;
  }
}

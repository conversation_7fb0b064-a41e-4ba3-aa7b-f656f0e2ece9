"""
RAK3172 LoRaWAN Module Power Model
=================================

This module contains the power consumption model for RAK3172 LoRaWAN module
based on datasheet specifications and real-world measurements.

Data sources:
- RAK3172 Datasheet: https://docs.rakwireless.com/product-categories/wisduo/rak3172-module/datasheet/
- STM32WLE5CC datasheet (underlying chip)
- Forum discussions and real-world measurements
"""

import numpy as np
from scipy.interpolate import interp1d
from typing import Dict, Tuple, Optional
import logging

logger = logging.getLogger(__name__)


class RAK3172PowerModel:
    """
    Power consumption model for RAK3172 LoRaWAN module.
    
    Based on STM32WLE5CC chip with real datasheet values and interpolation
    for missing data points.
    """
    
    def __init__(self, config=None):
        """Initialize the RAK3172 power model with configurable parameters."""

        # Use configuration if provided, otherwise use datasheet defaults
        if config and hasattr(config, 'lora_config'):
            lora_config = config.lora_config

            # TX Power consumption data (from FIRMWARE MEASUREMENTS)
            self.tx_power_data = {
                22: lora_config.tx_current_22dbm,
                20: lora_config.tx_current_20dbm,
                17: lora_config.tx_current_17dbm,
                14: lora_config.tx_current_14dbm,
                10: lora_config.tx_current_10dbm,
                7: lora_config.tx_current_7dbm
            }

            # Sleep mode power consumption (from FIRMWARE MEASUREMENTS)
            self.sleep_modes = {
                'stop2': lora_config.sleep_current_stop2,
                'stop1': lora_config.sleep_current_stop1,
                'stop0': lora_config.sleep_current_stop0,
                'sleep': lora_config.sleep_current_sleep
            }

            # Timing parameters (from FIRMWARE MEASUREMENTS)
            self.wakeup_time_ms = lora_config.wakeup_time_ms
            self.boot_time_ms = lora_config.boot_time_ms
            self.sleep_entry_time_ms = lora_config.sleep_entry_time_ms

        else:
            # DATASHEET DEFAULTS - SHOULD BE REPLACED WITH MEASURED VALUES
            self.tx_power_data = {
                22: 120.0,  # mA at 22 dBm - MEASURE WITH REAL FIRMWARE
                20: 87.0,   # mA at 20 dBm - MEASURE WITH REAL FIRMWARE
                17: 44.0,   # mA at 17 dBm - MEASURE WITH REAL FIRMWARE
                14: 24.0,   # mA at 14 dBm - MEASURE WITH REAL FIRMWARE
                10: 17.0,   # mA at 10 dBm - MEASURE WITH REAL FIRMWARE
                7:  15.0,   # mA at 7 dBm - MEASURE WITH REAL FIRMWARE
            }

            # Sleep mode power consumption - MEASURE WITH REAL FIRMWARE
            self.sleep_modes = {
                'stop2': 1.69,      # µA - MEASURE WITH REAL FIRMWARE
                'stop1': 3.5,       # µA - MEASURE WITH REAL FIRMWARE
                'stop0': 8.0,       # µA - MEASURE WITH REAL FIRMWARE
                'sleep': 15.0,      # µA - MEASURE WITH REAL FIRMWARE
            }

            # Timing parameters - MEASURE WITH REAL FIRMWARE
            self.wakeup_time_ms = 5.0    # Wake-up time - MEASURE
            self.boot_time_ms = 100.0    # Boot time - MEASURE
            self.sleep_entry_time_ms = 2.0  # Sleep entry time - MEASURE

        # RX Power consumption (from datasheet)
        self.rx_current = 5.22  # mA
        
        # Operating parameters
        self.operating_voltage_range = (2.0, 3.6)  # Volts
        self.operating_temp_range = (-40, 85)      # Celsius (RAK3172-T)
        
        # LoRaWAN timing parameters
        self.spreading_factors = {
            'SF7':  {'symbol_time_ms': 1.024, 'min_airtime_ms': 41.2},
            'SF8':  {'symbol_time_ms': 2.048, 'min_airtime_ms': 72.2},
            'SF9':  {'symbol_time_ms': 4.096, 'min_airtime_ms': 123.4},
            'SF10': {'symbol_time_ms': 8.192, 'min_airtime_ms': 226.3},
            'SF11': {'symbol_time_ms': 16.384, 'min_airtime_ms': 414.7},
            'SF12': {'symbol_time_ms': 32.768, 'min_airtime_ms': 759.8},
        }
        
        # Temperature coefficients
        if config and hasattr(config, 'lora_config'):
            self.temp_coefficient_tx = config.lora_config.tx_temp_coefficient
            self.temp_coefficient_sleep = config.lora_config.sleep_temp_coefficient
        else:
            self.temp_coefficient_tx = 0.002   # 0.2% per °C - MEASURE WITH REAL HARDWARE
            self.temp_coefficient_sleep = 0.01 # 1% per °C - MEASURE WITH REAL HARDWARE
        
        # Create interpolation functions
        self._create_interpolation_functions()
        
        logger.info("RAK3172 Power Model initialized")

    def update_configuration(self, config):
        """Update configuration and rebuild interpolation functions"""
        if config and hasattr(config, 'lora_config'):
            lora_config = config.lora_config

            # Update TX power data
            self.tx_power_data = {
                22: lora_config.tx_current_22dbm,
                20: lora_config.tx_current_20dbm,
                17: lora_config.tx_current_17dbm,
                14: lora_config.tx_current_14dbm,
                10: lora_config.tx_current_10dbm,
                7: lora_config.tx_current_7dbm
            }

            # Update sleep modes
            self.sleep_modes = {
                'stop2': lora_config.sleep_current_stop2,
                'stop1': lora_config.sleep_current_stop1,
                'stop0': lora_config.sleep_current_stop0,
                'sleep': lora_config.sleep_current_sleep
            }

            # Update temperature coefficients
            self.temp_coefficient_tx = lora_config.tx_temp_coefficient
            self.temp_coefficient_sleep = lora_config.sleep_temp_coefficient

            # Rebuild interpolation functions with new data
            self._create_interpolation_functions()

            logger.info("RAK3172 configuration updated")

    def _create_interpolation_functions(self):
        """Create interpolation functions for smooth power curves."""
        
        # TX Power interpolation
        power_levels = np.array(list(self.tx_power_data.keys()))
        tx_currents = np.array(list(self.tx_power_data.values()))
        
        # Sort by power level
        sort_idx = np.argsort(power_levels)
        power_levels = power_levels[sort_idx]
        tx_currents = tx_currents[sort_idx]
        
        self.tx_power_interpolator = interp1d(
            power_levels, tx_currents,
            kind='cubic',
            bounds_error=False,
            fill_value='extrapolate'
        )
    
    def get_tx_current(self, power_dbm: float, temperature: float = 25.0) -> float:
        """
        Get TX current consumption at specific power level and temperature.
        
        Args:
            power_dbm: TX power level in dBm (7-22 dBm)
            temperature: Temperature in Celsius
            
        Returns:
            TX current consumption in mA
        """
        # Clamp power level to valid range
        power_dbm = np.clip(power_dbm, 7, 22)
        
        # Get base current from interpolation
        base_current = float(self.tx_power_interpolator(power_dbm))
        
        # Apply temperature coefficient
        temp_factor = 1 + self.temp_coefficient_tx * (temperature - 25.0)
        current = base_current * temp_factor
        
        return max(current, 0.0)
    
    def get_rx_current(self, temperature: float = 25.0) -> float:
        """
        Get RX current consumption at specific temperature.
        
        Args:
            temperature: Temperature in Celsius
            
        Returns:
            RX current consumption in mA
        """
        # Apply temperature coefficient
        temp_factor = 1 + self.temp_coefficient_tx * (temperature - 25.0)
        current = self.rx_current * temp_factor
        
        return max(current, 0.0)
    
    def get_sleep_current(self, mode: str = 'stop2', temperature: float = 25.0) -> float:
        """
        Get sleep current consumption for specific mode and temperature.
        
        Args:
            mode: Sleep mode ('stop2', 'stop1', 'stop0', 'sleep')
            temperature: Temperature in Celsius
            
        Returns:
            Sleep current consumption in µA
        """
        if mode not in self.sleep_modes:
            logger.warning(f"Unknown sleep mode: {mode}, using 'stop2'")
            mode = 'stop2'
        
        base_current = self.sleep_modes[mode]
        
        # Apply temperature coefficient
        temp_factor = 1 + self.temp_coefficient_sleep * (temperature - 25.0)
        current = base_current * temp_factor
        
        return max(current, 0.0)
    
    def calculate_airtime_ms(self, spreading_factor: str, payload_bytes: int = 12) -> float:
        """
        Calculate LoRaWAN transmission airtime.
        
        Args:
            spreading_factor: SF7, SF8, SF9, SF10, SF11, or SF12
            payload_bytes: Payload size in bytes (default: 12 for temperature data)
            
        Returns:
            Airtime in milliseconds
        """
        if spreading_factor not in self.spreading_factors:
            logger.warning(f"Unknown SF: {spreading_factor}, using SF7")
            spreading_factor = 'SF7'
        
        sf_data = self.spreading_factors[spreading_factor]
        
        # Simplified airtime calculation (more accurate formula would consider coding rate, etc.)
        # This is a reasonable approximation for typical LoRaWAN packets
        base_airtime = sf_data['min_airtime_ms']
        
        # Add payload-dependent time (rough approximation)
        payload_factor = 1 + (payload_bytes - 12) * 0.1  # 10% per extra byte
        airtime = base_airtime * max(payload_factor, 1.0)
        
        return airtime
    
    def calculate_tx_energy_mah(self, 
                               power_dbm: float,
                               spreading_factor: str = 'SF7',
                               payload_bytes: int = 12,
                               temperature: float = 25.0) -> Tuple[float, Dict[str, float]]:
        """
        Calculate energy consumption for a single transmission.
        
        Args:
            power_dbm: TX power level in dBm
            spreading_factor: LoRaWAN spreading factor
            payload_bytes: Payload size in bytes
            temperature: Temperature in Celsius
            
        Returns:
            Tuple of (energy_mAh, breakdown_dict)
        """
        breakdown = {}
        
        # Get TX current
        tx_current_ma = self.get_tx_current(power_dbm, temperature)
        breakdown['tx_current_mA'] = tx_current_ma
        breakdown['power_dbm'] = power_dbm
        breakdown['temperature_C'] = temperature
        
        # Get airtime
        airtime_ms = self.calculate_airtime_ms(spreading_factor, payload_bytes)
        airtime_hours = airtime_ms / (1000 * 3600)  # Convert to hours
        breakdown['airtime_ms'] = airtime_ms
        breakdown['spreading_factor'] = spreading_factor
        breakdown['payload_bytes'] = payload_bytes
        
        # Calculate energy
        energy_mah = tx_current_ma * airtime_hours
        breakdown['energy_per_tx_mAh'] = energy_mah
        
        return energy_mah, breakdown
    
    def calculate_daily_energy_mah(self,
                                  tx_power_dbm: float,
                                  tx_interval_minutes: int,
                                  spreading_factor: str = 'SF7',
                                  sleep_mode: str = 'stop2',
                                  sensor_measurements_per_tx: int = 1,
                                  temperature: float = 25.0) -> Tuple[float, Dict[str, float]]:
        """
        Calculate total daily energy consumption.
        
        Args:
            tx_power_dbm: TX power level in dBm
            tx_interval_minutes: Transmission interval in minutes
            spreading_factor: LoRaWAN spreading factor
            sleep_mode: Sleep mode between transmissions
            sensor_measurements_per_tx: Number of sensor readings per transmission
            temperature: Operating temperature in Celsius
            
        Returns:
            Tuple of (daily_energy_mAh, detailed_breakdown)
        """
        breakdown = {}
        
        # Calculate transmissions per day
        tx_per_day = (24 * 60) / tx_interval_minutes
        breakdown['tx_per_day'] = tx_per_day
        breakdown['tx_interval_minutes'] = tx_interval_minutes
        
        # TX energy per transmission
        tx_energy_per_tx, tx_breakdown = self.calculate_tx_energy_mah(
            tx_power_dbm, spreading_factor, 12, temperature
        )
        breakdown.update(tx_breakdown)
        
        # Daily TX energy
        daily_tx_energy = tx_energy_per_tx * tx_per_day
        breakdown['daily_tx_energy_mAh'] = daily_tx_energy
        
        # Sleep energy (between transmissions)
        sleep_current_ua = self.get_sleep_current(sleep_mode, temperature)
        sleep_current_ma = sleep_current_ua / 1000  # Convert µA to mA
        
        # Sleep time per day (total time minus TX time)
        total_tx_time_hours = (breakdown['airtime_ms'] * tx_per_day) / (1000 * 3600)
        sleep_time_hours = 24 - total_tx_time_hours
        
        daily_sleep_energy = sleep_current_ma * sleep_time_hours
        breakdown['sleep_current_uA'] = sleep_current_ua
        breakdown['sleep_mode'] = sleep_mode
        breakdown['sleep_time_hours'] = sleep_time_hours
        breakdown['daily_sleep_energy_mAh'] = daily_sleep_energy
        
        # Sensor energy (estimated - will be refined with MAX31820 model)
        sensor_current_ma = 1.5  # mA during measurement (from MAX31820 datasheet)
        sensor_time_ms = 750     # ms for 12-bit conversion
        sensor_time_hours = sensor_time_ms / (1000 * 3600)
        
        daily_sensor_measurements = sensor_measurements_per_tx * tx_per_day
        daily_sensor_energy = sensor_current_ma * sensor_time_hours * daily_sensor_measurements
        breakdown['daily_sensor_measurements'] = daily_sensor_measurements
        breakdown['daily_sensor_energy_mAh'] = daily_sensor_energy
        
        # Total daily energy
        total_daily_energy = daily_tx_energy + daily_sleep_energy + daily_sensor_energy
        breakdown['total_daily_energy_mAh'] = total_daily_energy
        
        # Energy breakdown percentages
        if total_daily_energy > 0:
            breakdown['tx_energy_percent'] = (daily_tx_energy / total_daily_energy) * 100
            breakdown['sleep_energy_percent'] = (daily_sleep_energy / total_daily_energy) * 100
            breakdown['sensor_energy_percent'] = (daily_sensor_energy / total_daily_energy) * 100
        
        return total_daily_energy, breakdown


def test_rak3172_model():
    """Test the RAK3172 power model with realistic scenarios."""
    rak3172 = RAK3172PowerModel()
    
    print("=== RAK3172 Power Model Test ===")
    
    # Test 1: Typical scenario
    print(f"\nTest 1 - Typical scenario:")
    daily_energy, breakdown = rak3172.calculate_daily_energy_mah(
        tx_power_dbm=14,
        tx_interval_minutes=15,
        spreading_factor='SF7',
        sleep_mode='stop2',
        temperature=20.0
    )
    print(f"TX Power: 14 dBm, Interval: 15 min, SF7, Stop2 sleep, 20°C")
    print(f"TX Current: {breakdown['tx_current_mA']:.1f} mA")
    print(f"Sleep Current: {breakdown['sleep_current_uA']:.2f} µA")
    print(f"Transmissions/day: {breakdown['tx_per_day']:.1f}")
    print(f"Daily Energy: {daily_energy:.3f} mAh")
    print(f"  - TX: {breakdown['tx_energy_percent']:.1f}%")
    print(f"  - Sleep: {breakdown['sleep_energy_percent']:.1f}%")
    print(f"  - Sensor: {breakdown['sensor_energy_percent']:.1f}%")
    
    # Test 2: High power, frequent transmissions
    print(f"\nTest 2 - High power, frequent transmissions:")
    daily_energy, breakdown = rak3172.calculate_daily_energy_mah(
        tx_power_dbm=20,
        tx_interval_minutes=5,
        spreading_factor='SF12',
        sleep_mode='stop2',
        temperature=-20.0
    )
    print(f"TX Power: 20 dBm, Interval: 5 min, SF12, Stop2 sleep, -20°C")
    print(f"TX Current: {breakdown['tx_current_mA']:.1f} mA")
    print(f"Airtime: {breakdown['airtime_ms']:.1f} ms")
    print(f"Transmissions/day: {breakdown['tx_per_day']:.1f}")
    print(f"Daily Energy: {daily_energy:.3f} mAh")
    print(f"  - TX: {breakdown['tx_energy_percent']:.1f}%")
    print(f"  - Sleep: {breakdown['sleep_energy_percent']:.1f}%")
    print(f"  - Sensor: {breakdown['sensor_energy_percent']:.1f}%")


if __name__ == "__main__":
    test_rak3172_model()

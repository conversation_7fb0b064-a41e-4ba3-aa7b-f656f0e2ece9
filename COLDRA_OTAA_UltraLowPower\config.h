/**
 * COLDRA Configuration File - OTAA Variant
 * @version 2.1.0
 * @date 2025-09-06
 * 
 * CRITICAL: These settings directly impact power consumption
 * Modify with extreme caution
 * 
 * v2.1.0 OPTIMIZATION NOTES:
 * - Implemented state machine for temperature reading
 * - Eliminated 750ms blocking delay during sensor conversion
 * - System can now deep sleep during temperature conversion
 * - Expected power consumption: 2-3µA sleep, 15+ year battery life
 */

#ifndef _CONFIG_H_
#define _CONFIG_H_

// Firmware Version
#define FIRMWARE_VERSION "2.2.0-OTAA"

// ========================================
// HARDWARE CONFIGURATION
// ========================================

// Temperature Sensor (MAX31820)
#define TEMP_SENSOR_PIN     WB_IO1  // PA9 with external 4.7kΩ pullup
#define TEMP_SENSOR_PORT    GPIOA
#define TEMP_SENSOR_GPIO    GPIO_PIN_9

// Status LED (Active-Low)
#define LED_STATUS_PIN      WB_IO2  // PA10
#define LED_STATUS_PORT     GPIOA
#define LED_STATUS_GPIO     GPIO_PIN_10

// Pin Modes for Low Power
#define PIN_MODE_INPUT_PULLUP   0
#define PIN_MODE_OUTPUT         1
#define PIN_MODE_ANALOG         2

// ========================================
// LORAWAN CONFIGURATION
// ========================================

// Network Settings
#define OTAA_BAND           RAK_REGION_US915
#define CHANNEL_MASK        0x0002  // Sub-band 2 for TTN
#define LORAWAN_PORT        2        // Application port

// Power Optimization Settings
#define TX_POWER            7        // 14dBm (conservative for battery)
#define DR_SETTING          2        // SF10 starting data rate
#define MAX_JOIN_RETRIES    3        // Limit join attempts

// Timing Configuration
#define TX_INTERVAL_MS      300000   // 5 minutes (300 seconds)
#define JOIN_RETRY_DELAY_MS 30000    // 30 seconds between join attempts

// ========================================
// CREDENTIAL STORAGE
// ========================================

// OTAA credentials are managed by RUI3 internal storage via AT commands
// - AppEUI: Set by AT+APPEUI= command, read by api.lorawan.appeui.get()
// - AppKey: Set by AT+APPKEY= command, read by api.lorawan.appkey.get() 
// - DevEUI: Always read from chip using api.lorawan.deui.get()
// 
// This approach is compatible with provision_credentials.py factory tool

// ========================================
// POWER MANAGEMENT
// ========================================

// Target Power Consumption
#define TARGET_SLEEP_CURRENT_UA     2.5   // Target: 2.5µA deep sleep
#define MAX_SLEEP_CURRENT_UA        3.0   // Maximum acceptable: 3µA

// Wake Sources
#define WAKE_SOURCE_TIMER           0x01
#define WAKE_SOURCE_GPIO            0x02
#define WAKE_SOURCE_LORAWAN         0x04

// ========================================
// TEMPERATURE SENSOR
// ========================================

// MAX31820 Configuration
#define MAX31820_RESOLUTION_12BIT   0x7F
#define MAX31820_CONVERSION_TIME_MS 750   // 12-bit conversion time

// Temperature Limits (in hundredths of degree C)
#define TEMP_MIN_VALID             -5500  // -55.00°C
#define TEMP_MAX_VALID             12500  // +125.00°C

// ========================================
// DIAGNOSTIC/DEBUG (DISABLE IN PRODUCTION!)
// ========================================

// #define ENABLE_SERIAL_DEBUG      // Uncomment only for debugging
// #define ENABLE_POWER_PROFILING   // Uncomment for power measurement

#ifdef ENABLE_SERIAL_DEBUG
    #define DEBUG_PRINT(x)      Serial.print(x)
    #define DEBUG_PRINTLN(x)    Serial.println(x)
    #define DEBUG_PRINTF(...)   Serial.printf(__VA_ARGS__)
#else
    #define DEBUG_PRINT(x)
    #define DEBUG_PRINTLN(x)
    #define DEBUG_PRINTF(...)
#endif

// ========================================
// PROVISIONING MODE
// ========================================

// Provisioning trigger (hold time in ms)
#define PROVISION_BUTTON_PIN       WB_IO3
#define PROVISION_HOLD_TIME_MS     5000
#define PROVISION_BAUD_RATE        115200

// Provisioning commands
#define CMD_SET_APPEUI             "AT+APPEUI="
#define CMD_SET_APPKEY             "AT+APPKEY="
#define CMD_GET_DEVEUI             "AT+DEVEUI?"
#define CMD_SAVE_CREDENTIALS       "AT+SAVE"
#define CMD_REBOOT                 "AT+REBOOT"

// ========================================
// ERROR CODES
// ========================================

#define ERR_NONE                    0x00
#define ERR_SENSOR_NOT_FOUND        0x01
#define ERR_SENSOR_CRC              0x02
#define ERR_JOIN_FAILED             0x03
#define ERR_TX_FAILED               0x04
#define ERR_FLASH_READ              0x05
#define ERR_FLASH_WRITE             0x06
#define ERR_INVALID_CREDENTIALS     0x07
#define ERR_LOW_BATTERY             0x08

// ========================================
// BATTERY MONITORING
// ========================================

#define BATTERY_LOW_THRESHOLD_MV    2000  // 2.0V threshold
#define BATTERY_CRITICAL_MV         1800  // 1.8V critical

// ========================================
// OPTIMIZATION FLAGS
// ========================================

// Compiler optimization for size
#pragma GCC optimize ("Os")

// Disable unused peripherals
#define DISABLE_UNUSED_PERIPHERALS

// Use minimal stack size
#define MINIMAL_STACK_SIZE

#endif // _CONFIG_H_
"""
Configuration Manager for Battery Life Assessment Tool
====================================================

Manages all configurable parameters that should be measured by firmware team
rather than assumed from datasheets.
"""

import json
import os
from typing import Dict, Any, Optional
from dataclasses import dataclass, asdict
from pathlib import Path


@dataclass
class LoRaModuleConfig:
    """LoRaWAN module configuration - all values should be measured by firmware team"""
    
    # TX Current Consumption (mA) - MEASURE WITH REAL FIRMWARE
    tx_current_7dbm: float = 15.0      # Default from datasheet - REPLACE WITH MEASURED
    tx_current_10dbm: float = 17.0     # Default from datasheet - REPLACE WITH MEASURED  
    tx_current_14dbm: float = 24.0     # Default from datasheet - REPLACE WITH MEASURED
    tx_current_17dbm: float = 44.0     # Default from datasheet - REPLACE WITH MEASURED
    tx_current_20dbm: float = 87.0     # Default from datasheet - R<PERSON><PERSON>CE WITH MEASURED
    tx_current_22dbm: float = 120.0    # Default from datasheet - REPLACE WITH MEASURED
    
    # Sleep Current Consumption (µA) - MEASURE WITH REAL FIRMWARE
    sleep_current_stop0: float = 8.5   # Default from datasheet - REPLACE WITH MEASURED
    sleep_current_stop1: float = 2.1   # Default from datasheet - REPLACE WITH MEASURED
    sleep_current_stop2: float = 1.69  # Default from datasheet - REPLACE WITH MEASURED
    sleep_current_sleep: float = 1.5   # Default from datasheet - REPLACE WITH MEASURED
    
    # Timing Parameters (ms) - MEASURE WITH REAL FIRMWARE
    wakeup_time_ms: float = 5.0        # Time to wake from sleep - MEASURE
    sleep_entry_time_ms: float = 2.0   # Time to enter sleep - MEASURE
    boot_time_ms: float = 100.0        # Boot/initialization time - MEASURE
    
    # Boot Current Spike - MEASURE WITH REAL FIRMWARE
    boot_current_ma: float = 50.0      # Peak current during boot - MEASURE
    
    # Airtime Adjustments - MEASURE WITH REAL FIRMWARE
    preamble_symbols: int = 8          # Real preamble length - MEASURE
    sync_word_ms: float = 2.0          # Sync word time - MEASURE
    processing_delay_ms: float = 1.0   # Firmware processing delay - MEASURE
    
    # Temperature Effects - MEASURE WITH REAL HARDWARE
    tx_temp_coefficient: float = -0.005  # %/°C change in TX current - MEASURE
    sleep_temp_coefficient: float = 0.02 # %/°C change in sleep current - MEASURE
    
    # Crystal Accuracy - MEASURE WITH REAL HARDWARE
    crystal_accuracy_ppm: float = 20.0  # Crystal accuracy in ppm - MEASURE


@dataclass
class TemperatureSensorConfig:
    """Temperature sensor configuration - all values should be measured"""
    
    # Current Consumption - MEASURE WITH REAL FIRMWARE
    active_current_ma: float = 1.5     # Current during conversion - MEASURE
    standby_current_ua: float = 0.75   # Standby current - MEASURE
    
    # Timing - MEASURE WITH REAL FIRMWARE
    conversion_time_9bit_ms: float = 93.75   # 9-bit conversion time - MEASURE
    conversion_time_10bit_ms: float = 187.5  # 10-bit conversion time - MEASURE
    conversion_time_11bit_ms: float = 375.0  # 11-bit conversion time - MEASURE
    conversion_time_12bit_ms: float = 750.0  # 12-bit conversion time - MEASURE
    
    # 1-Wire Bus Parameters - MEASURE WITH REAL HARDWARE
    bus_pullup_current_ua: float = 50.0      # 1-Wire pullup current - MEASURE
    parasitic_power_current_ma: float = 0.0  # If using parasitic power - MEASURE
    
    # Temperature Effects - MEASURE WITH REAL HARDWARE
    temp_coefficient: float = 0.01     # %/°C change in current - MEASURE


@dataclass
class SystemLevelConfig:
    """System-level parameters often missed - all should be measured"""
    
    # Microcontroller (if separate from LoRa module)
    mcu_sleep_current_ua: float = 0.0   # MCU sleep current - MEASURE
    mcu_active_current_ma: float = 0.0  # MCU active current - MEASURE
    
    # Real-Time Clock
    rtc_current_ua: float = 1.0         # RTC current consumption - MEASURE
    
    # Watchdog Timer
    watchdog_current_ua: float = 0.5    # Watchdog current - MEASURE
    
    # GPIO Leakage
    gpio_leakage_ua: float = 0.1        # Total GPIO leakage - MEASURE
    
    # PCB Leakage
    pcb_leakage_ua: float = 0.5         # PCB leakage current - MEASURE
    
    # Voltage Regulator
    regulator_quiescent_ua: float = 10.0  # Regulator quiescent current - MEASURE
    regulator_efficiency: float = 0.85    # Regulator efficiency - MEASURE
    
    # Brown-out Detection
    bod_current_ua: float = 2.0         # Brown-out detector current - MEASURE
    
    # Flash Memory
    flash_retention_ua: float = 0.1     # Flash retention current - MEASURE
    
    # External Components
    led_current_ma: float = 2.0         # LED current when on - MEASURE
    led_on_time_ms: float = 100.0       # LED on time per transmission - MEASURE


@dataclass
class BatteryConfig:
    """Battery configuration - should be measured/validated"""
    
    # Battery Type
    battery_type: str = "Energizer L91 AA"
    battery_count: int = 2
    
    # Capacity Parameters - VALIDATE WITH REAL BATTERIES
    nominal_capacity_mah: float = 3000.0    # Nominal capacity - VALIDATE
    
    # Temperature Effects - MEASURE WITH REAL BATTERIES
    capacity_temp_coefficients: Dict[float, float] = None  # Will be set in __post_init__
    
    # Load Effects - MEASURE WITH REAL BATTERIES  
    capacity_load_coefficients: Dict[float, float] = None  # Will be set in __post_init__
    
    # Voltage Cutoff - MEASURE WITH REAL APPLICATION
    cutoff_voltage: float = 2.0         # Application cutoff voltage - MEASURE
    
    # Self-discharge - VALIDATE WITH REAL BATTERIES
    self_discharge_percent_per_year: float = 1.0  # Self-discharge rate - VALIDATE
    
    # Aging Effects - MEASURE IF LONG-TERM DEPLOYMENT
    aging_factor_per_year: float = 0.02  # Capacity loss per year - MEASURE
    
    def __post_init__(self):
        if self.capacity_temp_coefficients is None:
            # Default temperature coefficients - REPLACE WITH MEASURED VALUES
            self.capacity_temp_coefficients = {
                -40.0: 0.33,  # 33% capacity at -40°C - MEASURE
                -20.0: 0.67,  # 67% capacity at -20°C - MEASURE
                0.0: 0.85,    # 85% capacity at 0°C - MEASURE
                20.0: 1.0,    # 100% capacity at 20°C - MEASURE
                40.0: 1.05,   # 105% capacity at 40°C - MEASURE
                60.0: 1.08    # 108% capacity at 60°C - MEASURE
            }
        
        if self.capacity_load_coefficients is None:
            # Default load coefficients - REPLACE WITH MEASURED VALUES
            self.capacity_load_coefficients = {
                1.0: 1.0,     # 100% capacity at 1mA - MEASURE
                10.0: 0.98,   # 98% capacity at 10mA - MEASURE
                25.0: 0.95,   # 95% capacity at 25mA - MEASURE
                50.0: 0.90,   # 90% capacity at 50mA - MEASURE
                100.0: 0.80,  # 80% capacity at 100mA - MEASURE
                250.0: 0.65   # 65% capacity at 250mA - MEASURE
            }


@dataclass
class FirmwareConfig:
    """Firmware-specific parameters - all should be measured"""
    
    # Error Handling
    retry_attempts: int = 3             # Number of retry attempts - CONFIGURE
    retry_current_ma: float = 25.0      # Current during retry - MEASURE
    retry_delay_ms: float = 1000.0      # Delay between retries - CONFIGURE
    
    # Diagnostic Features
    debug_enabled: bool = False         # Debug features enabled - CONFIGURE
    debug_current_ma: float = 5.0       # Debug current overhead - MEASURE
    
    # Over-the-Air Updates
    ota_enabled: bool = False           # OTA capability - CONFIGURE
    ota_current_ma: float = 30.0        # OTA current consumption - MEASURE
    
    # Security/Encryption
    encryption_enabled: bool = True     # Encryption enabled - CONFIGURE
    encryption_time_ms: float = 10.0    # Encryption processing time - MEASURE
    encryption_current_ma: float = 20.0 # Encryption current overhead - MEASURE
    
    # Data Logging
    logging_enabled: bool = False       # Local data logging - CONFIGURE
    logging_current_ma: float = 2.0     # Logging current - MEASURE
    flash_write_current_ma: float = 15.0 # Flash write current - MEASURE


@dataclass
class EnvironmentalConfig:
    """Environmental and application-specific parameters"""
    
    # Operating Temperature Range - MEASURE IN REAL APPLICATION
    min_operating_temp: float = -40.0   # Minimum operating temperature - MEASURE
    max_operating_temp: float = 60.0    # Maximum operating temperature - MEASURE
    
    # Network Conditions - MEASURE IN REAL DEPLOYMENT
    packet_loss_rate: float = 0.05      # Expected packet loss rate - MEASURE
    network_congestion_factor: float = 1.1  # Congestion retry factor - MEASURE
    
    # Adaptive Data Rate - CONFIGURE BASED ON NETWORK
    adr_enabled: bool = True            # ADR enabled - CONFIGURE
    adr_margin_db: float = 10.0         # ADR margin - CONFIGURE


class ConfigurationManager:
    """Manages all configuration parameters for the battery life tool"""
    
    def __init__(self, config_file: Optional[str] = None):
        self.config_file = config_file or "battery_config.json"
        self.lora_config = LoRaModuleConfig()
        self.sensor_config = TemperatureSensorConfig()
        self.system_config = SystemLevelConfig()
        self.battery_config = BatteryConfig()
        self.firmware_config = FirmwareConfig()
        self.environmental_config = EnvironmentalConfig()
        
        # Load existing configuration if available
        self.load_configuration()
    
    def load_configuration(self) -> bool:
        """Load configuration from JSON file"""
        try:
            if os.path.exists(self.config_file):
                with open(self.config_file, 'r') as f:
                    config_data = json.load(f)
                
                # Update configurations with loaded data
                if 'lora_config' in config_data:
                    self.lora_config = LoRaModuleConfig(**config_data['lora_config'])
                if 'sensor_config' in config_data:
                    self.sensor_config = TemperatureSensorConfig(**config_data['sensor_config'])
                if 'system_config' in config_data:
                    self.system_config = SystemLevelConfig(**config_data['system_config'])
                if 'battery_config' in config_data:
                    self.battery_config = BatteryConfig(**config_data['battery_config'])
                if 'firmware_config' in config_data:
                    self.firmware_config = FirmwareConfig(**config_data['firmware_config'])
                if 'environmental_config' in config_data:
                    self.environmental_config = EnvironmentalConfig(**config_data['environmental_config'])
                
                return True
        except Exception as e:
            print(f"Warning: Could not load configuration: {e}")
        
        return False
    
    def save_configuration(self) -> bool:
        """Save current configuration to JSON file"""
        try:
            config_data = {
                'lora_config': asdict(self.lora_config),
                'sensor_config': asdict(self.sensor_config),
                'system_config': asdict(self.system_config),
                'battery_config': asdict(self.battery_config),
                'firmware_config': asdict(self.firmware_config),
                'environmental_config': asdict(self.environmental_config)
            }
            
            with open(self.config_file, 'w') as f:
                json.dump(config_data, f, indent=2)
            
            return True
        except Exception as e:
            print(f"Error saving configuration: {e}")
            return False
    
    def get_all_configs(self) -> Dict[str, Any]:
        """Get all configurations as dictionary"""
        return {
            'lora_config': asdict(self.lora_config),
            'sensor_config': asdict(self.sensor_config),
            'system_config': asdict(self.system_config),
            'battery_config': asdict(self.battery_config),
            'firmware_config': asdict(self.firmware_config),
            'environmental_config': asdict(self.environmental_config)
        }
    
    def update_config(self, config_type: str, updates: Dict[str, Any]) -> bool:
        """Update specific configuration section"""
        try:
            if config_type == 'lora_config':
                for key, value in updates.items():
                    if hasattr(self.lora_config, key):
                        setattr(self.lora_config, key, value)
            elif config_type == 'sensor_config':
                for key, value in updates.items():
                    if hasattr(self.sensor_config, key):
                        setattr(self.sensor_config, key, value)
            elif config_type == 'system_config':
                for key, value in updates.items():
                    if hasattr(self.system_config, key):
                        setattr(self.system_config, key, value)
            elif config_type == 'battery_config':
                for key, value in updates.items():
                    if hasattr(self.battery_config, key):
                        setattr(self.battery_config, key, value)
            elif config_type == 'firmware_config':
                for key, value in updates.items():
                    if hasattr(self.firmware_config, key):
                        setattr(self.firmware_config, key, value)
            elif config_type == 'environmental_config':
                for key, value in updates.items():
                    if hasattr(self.environmental_config, key):
                        setattr(self.environmental_config, key, value)
            else:
                return False
            
            return True
        except Exception as e:
            print(f"Error updating configuration: {e}")
            return False

#!/usr/bin/env python3
"""
Test the configuration API endpoints
"""

import requests
import json

def test_configuration_api():
    print('=== TESTING CONFIGURATION API ===')

    try:
        # Test get configuration
        response = requests.get('http://localhost:8001/get-configuration')
        if response.status_code == 200:
            config = response.json()
            print('✅ GET configuration successful')
            lora_config = config["configuration"]["lora_config"]
            sensor_config = config["configuration"]["sensor_config"]
            print(f'LoRa TX 14dBm current: {lora_config["tx_current_14dbm"]} mA')
            print(f'LoRa sleep current: {lora_config["sleep_current_stop2"]} µA')
            print(f'Sensor active current: {sensor_config["active_current_ma"]} mA')
        else:
            print(f'❌ GET configuration failed: {response.status_code}')
            return False

        # Test save configuration with modified values
        print('\n=== TESTING CONFIGURATION SAVE ===')
        test_config = {
            'lora_config': {
                'tx_current_14dbm': 30.0,  # Change from default 24.0 to 30.0
                'sleep_current_stop2': 2.0  # Change from default 1.69 to 2.0
            }
        }
        
        response = requests.post('http://localhost:8001/save-configuration', json=test_config)
        if response.status_code == 200:
            print('✅ SAVE configuration successful')
            result = response.json()
            new_lora_config = result['configuration']['lora_config']
            print(f'Updated TX 14dBm current: {new_lora_config["tx_current_14dbm"]} mA')
            print(f'Updated sleep current: {new_lora_config["sleep_current_stop2"]} µA')
            
            # Test if calculation uses new values
            print('\n=== TESTING CALCULATION WITH NEW CONFIG ===')
            calc_response = requests.post('http://localhost:8001/calculate-battery-life', json={
                'temperature': 20.0,
                'tx_power_dbm': 14,
                'tx_interval_minutes': 15,
                'spreading_factor': 'SF7',
                'sleep_mode': 'stop2',
                'sensor_resolution_bits': 12,
                'measurements_per_tx': 1,
                'led_enabled': True,
                'battery_count': 2
            })
            
            if calc_response.status_code == 200:
                calc_result = calc_response.json()
                print(f'✅ Calculation with new config:')
                print(f'   TX Current: {calc_result["lora_details"]["tx_current_ma"]} mA (should be 30.0)')
                print(f'   Sleep Current: {calc_result["lora_details"]["sleep_current_ua"]} µA (should be 2.0)')
                print(f'   Battery life: {calc_result["battery_life_years"]} years')
                
                # Verify the values changed
                if abs(calc_result["lora_details"]["tx_current_ma"] - 30.0) < 0.1:
                    print('✅ TX current successfully updated in calculations!')
                else:
                    print('❌ TX current NOT updated in calculations')
                    
                if abs(calc_result["lora_details"]["sleep_current_ua"] - 2.0) < 0.1:
                    print('✅ Sleep current successfully updated in calculations!')
                else:
                    print('❌ Sleep current NOT updated in calculations')
                    
            else:
                print('❌ Calculation with new config failed')
                print(f'Status: {calc_response.status_code}')
                print(f'Response: {calc_response.text}')
        else:
            print(f'❌ SAVE configuration failed: {response.status_code}')
            print(f'Response: {response.text}')

        # Test reset configuration
        print('\n=== TESTING CONFIGURATION RESET ===')
        reset_response = requests.post('http://localhost:8001/reset-configuration')
        if reset_response.status_code == 200:
            print('✅ RESET configuration successful')
            result = reset_response.json()
            reset_lora_config = result['configuration']['lora_config']
            print(f'Reset TX 14dBm current: {reset_lora_config["tx_current_14dbm"]} mA (should be 24.0)')
            print(f'Reset sleep current: {reset_lora_config["sleep_current_stop2"]} µA (should be 1.69)')
        else:
            print(f'❌ RESET configuration failed: {reset_response.status_code}')

    except Exception as e:
        print(f'❌ Configuration API test error: {e}')
        import traceback
        traceback.print_exc()
        return False
    
    return True

if __name__ == "__main__":
    success = test_configuration_api()
    if success:
        print('\n🎉 Configuration API tests completed!')
    else:
        print('\n❌ Configuration API tests failed!')

"""
Battery Life Assessment Tool - FastAPI Backend
==============================================

FastAPI backend server for the COLDRA battery life assessment tool.
Provides REST API endpoints for battery life calculations.
"""

from fastapi import FastAPI, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel, Field
from typing import Optional, Dict, Any
import logging
import sys
import os

# Add battery_life_tool directory to path to import models
battery_tool_dir = os.path.dirname(os.path.abspath(__file__))
battery_tool_dir = os.path.dirname(battery_tool_dir)  # Go up from backend/ to battery_life_tool/
sys.path.insert(0, battery_tool_dir)

from models.battery_life_calculator import BatteryLifeCalculator

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Initialize FastAPI app
app = FastAPI(
    title="COLDRA Battery Life Assessment API",
    description="REST API for calculating battery life of LoRaWAN temperature sensors",
    version="1.0.0",
    docs_url="/docs",
    redoc_url="/redoc"
)

# Configure CORS for frontend development
app.add_middleware(
    CORSMiddleware,
    allow_origins=["http://localhost:3000", "http://localhost:3001", "http://127.0.0.1:3000"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Initialize calculator
calculator = BatteryLifeCalculator()


class BatteryLifeRequest(BaseModel):
    """Request model for battery life calculation."""
    
    # Environmental parameters
    temperature: float = Field(
        default=20.0,
        ge=-50.0,
        le=70.0,
        description="Operating temperature in Celsius (-50°C to +70°C)"
    )
    
    # LoRaWAN parameters
    tx_power_dbm: float = Field(
        default=14,
        ge=7,
        le=22,
        description="LoRaWAN TX power level in dBm (7-22 dBm)"
    )
    tx_interval_minutes: int = Field(
        default=15,
        ge=1,
        le=1440,
        description="Transmission interval in minutes (1-1440 min)"
    )
    spreading_factor: str = Field(
        default="SF7",
        regex="^SF(7|8|9|10|11|12)$",
        description="LoRaWAN spreading factor (SF7-SF12)"
    )
    sleep_mode: str = Field(
        default="stop2",
        regex="^(stop0|stop1|stop2|sleep)$",
        description="Sleep mode between transmissions"
    )
    
    # Sensor parameters
    sensor_resolution_bits: int = Field(
        default=12,
        ge=9,
        le=12,
        description="Temperature sensor resolution in bits (9-12)"
    )
    measurements_per_tx: int = Field(
        default=1,
        ge=1,
        le=10,
        description="Sensor measurements per transmission (1-10)"
    )
    
    # LED parameters
    led_enabled: bool = Field(
        default=True,
        description="Whether LED blink is enabled"
    )
    
    # Battery parameters
    battery_count: int = Field(
        default=2,
        ge=1,
        le=4,
        description="Number of AA batteries (1-4)"
    )


class BatteryLifeResponse(BaseModel):
    """Response model for battery life calculation."""
    
    # Main results
    battery_life_days: float = Field(description="Battery life in days")
    battery_life_years: float = Field(description="Battery life in years")
    daily_energy_mah: float = Field(description="Daily energy consumption in mAh")
    
    # Energy breakdown
    energy_breakdown: Dict[str, float] = Field(description="Energy distribution percentages")
    
    # Battery information
    battery_info: Dict[str, Any] = Field(description="Battery capacity and current information")
    
    # Confidence and metadata
    confidence_level: float = Field(description="Confidence level (0.0-1.0)")
    confidence_description: str = Field(description="Confidence level description")
    
    # Input parameters (for verification)
    input_parameters: Dict[str, Any] = Field(description="Input parameters used for calculation")


@app.get("/")
async def root():
    """Root endpoint with API information."""
    return {
        "message": "COLDRA Battery Life Assessment API",
        "version": "1.0.0",
        "docs": "/docs",
        "endpoints": {
            "calculate": "/calculate-battery-life",
            "health": "/health"
        }
    }


@app.get("/health")
async def health_check():
    """Health check endpoint."""
    return {
        "status": "healthy",
        "calculator_initialized": calculator is not None,
        "timestamp": "2025-09-12"
    }


@app.post("/calculate-battery-life", response_model=BatteryLifeResponse)
async def calculate_battery_life(request: BatteryLifeRequest):
    """
    Calculate battery life based on input parameters.
    
    Returns detailed battery life prediction with energy breakdown.
    """
    try:
        logger.info(f"Calculating battery life for: {request.dict()}")
        
        # Perform calculation
        battery_life_days, breakdown = calculator.calculate_battery_life(
            temperature=request.temperature,
            tx_power_dbm=request.tx_power_dbm,
            tx_interval_minutes=request.tx_interval_minutes,
            spreading_factor=request.spreading_factor,
            sleep_mode=request.sleep_mode,
            sensor_resolution_bits=request.sensor_resolution_bits,
            measurements_per_tx=request.measurements_per_tx,
            led_enabled=request.led_enabled,
            battery_count=request.battery_count
        )
        
        # Extract results
        battery_life_years = battery_life_days / 365.25
        daily_energy_mah = breakdown['energy_summary']['total_daily_energy_mAh']
        
        # Energy breakdown percentages
        energy_breakdown = {
            'lora_percent': breakdown['energy_summary'].get('lora_energy_percent', 0),
            'sensor_percent': breakdown['energy_summary'].get('sensor_energy_percent', 0),
            'led_percent': breakdown['energy_summary'].get('led_energy_percent', 0)
        }
        
        # Battery information
        battery_info = {
            'total_capacity_mah': breakdown['battery']['total_capacity_mAh'],
            'effective_capacity_mah': breakdown['battery']['effective_capacity_mAh'],
            'average_current_ma': breakdown['battery']['average_current_mA'],
            'peak_current_ma': breakdown['battery']['peak_current_mA'],
            'temperature_effect': f"{((breakdown['battery']['base_capacity_mAh'] - breakdown['battery']['effective_capacity_mAh']) / breakdown['battery']['base_capacity_mAh'] * 100):.1f}% capacity reduction"
        }
        
        # Confidence information
        confidence_level = breakdown['confidence']['overall']
        confidence_description = breakdown['confidence']['level']
        
        # Create response
        response = BatteryLifeResponse(
            battery_life_days=battery_life_days,
            battery_life_years=battery_life_years,
            daily_energy_mah=daily_energy_mah,
            energy_breakdown=energy_breakdown,
            battery_info=battery_info,
            confidence_level=confidence_level,
            confidence_description=confidence_description,
            input_parameters=request.dict()
        )
        
        logger.info(f"Calculation successful: {battery_life_years:.2f} years")
        return response
        
    except Exception as e:
        logger.error(f"Calculation error: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"Calculation failed: {str(e)}"
        )


@app.get("/presets")
async def get_presets():
    """Get predefined calculation presets."""
    presets = {
        "typical_freezer": {
            "name": "Typical Freezer",
            "description": "Standard freezer application (-20°C, moderate power)",
            "parameters": {
                "temperature": -20.0,
                "tx_power_dbm": 14,
                "tx_interval_minutes": 15,
                "spreading_factor": "SF7",
                "sleep_mode": "stop2"
            }
        },
        "room_temperature": {
            "name": "Room Temperature",
            "description": "Room temperature monitoring (20°C, low power)",
            "parameters": {
                "temperature": 20.0,
                "tx_power_dbm": 10,
                "tx_interval_minutes": 30,
                "spreading_factor": "SF7",
                "sleep_mode": "stop2"
            }
        },
        "extreme_cold": {
            "name": "Extreme Cold",
            "description": "Worst case scenario (-40°C, high power, frequent TX)",
            "parameters": {
                "temperature": -40.0,
                "tx_power_dbm": 20,
                "tx_interval_minutes": 5,
                "spreading_factor": "SF12",
                "sleep_mode": "stop2"
            }
        },
        "power_saving": {
            "name": "Power Saving",
            "description": "Maximum battery life configuration",
            "parameters": {
                "temperature": 0.0,
                "tx_power_dbm": 7,
                "tx_interval_minutes": 60,
                "spreading_factor": "SF7",
                "sleep_mode": "stop2",
                "sensor_resolution_bits": 9,
                "led_enabled": False
            }
        }
    }
    
    return {"presets": presets}


if __name__ == "__main__":
    import uvicorn
    
    logger.info("Starting COLDRA Battery Life Assessment API server...")
    uvicorn.run(
        "main:app",
        host="0.0.0.0",
        port=8000,
        reload=True,
        log_level="info"
    )

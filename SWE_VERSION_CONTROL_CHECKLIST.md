# SWE Version Control Checklist - COLDRA Firmware Builder Support

## Version: 1.1.0 | Last Updated: 2025-09-06

## Pre-Development Checklist
- [ ] Read AI_AGENT_HANDOFF_INSTRUCTIONS.md
- [ ] Read CRITICAL_REMINDERS.md
- [ ] Check current VERSION file
- [ ] Review recent CHANGELOG.md entries
- [ ] Understand power consumption targets

## During Development Checklist

### For ANY Code Change
- [ ] Implement the requested feature/fix
- [ ] Test functionality thoroughly
- [ ] Measure power consumption impact
- [ ] Verify no regression in sleep current

### Code Quality Checks
- [ ] No `delay()` functions used
- [ ] No active `loop()` function
- [ ] No `Serial.print()` in production code
- [ ] All unused pins configured as INPUT_PULLUP
- [ ] Power management APIs used correctly

### Testing Requirements
- [ ] Deep sleep current < 3µA verified
- [ ] Temperature reading functional
- [ ] LoRaWAN transmission successful
- [ ] Battery monitoring operational
- [ ] 5-minute timer cycle working

## Post-Development Checklist (MANDATORY)

### Version Control Updates
- [ ] Increment VERSION file (e.g., 1.0.0 → 1.0.1)
- [ ] Update version in all code file headers
- [ ] Add detailed CHANGELOG.md entry with:
  - [ ] New version number
  - [ ] Date of change
  - [ ] List of all changes under Added/Changed/Fixed/Removed
  - [ ] Any breaking changes noted
  - [ ] Power consumption impact documented

### Documentation Updates
- [ ] Update README.md if user-facing changes
- [ ] Update AI_AGENT_HANDOFF_INSTRUCTIONS.md with new context
- [ ] Update PROJECT_PLAN.md with completed tasks
- [ ] Update this checklist if process changes
- [ ] Update API documentation if interfaces changed

### Final Verification
- [ ] All files saved
- [ ] Version numbers consistent across all files
- [ ] Documentation matches implementation
- [ ] Power targets still met
- [ ] No temporary debug code remains

## Version Numbering Guidelines

### Semantic Versioning (MAJOR.MINOR.PATCH)
- **PATCH (x.x.1):** Bug fixes, minor improvements, documentation updates
- **MINOR (x.1.0):** New features, non-breaking changes, optimizations
- **MAJOR (1.0.0):** Breaking changes, major architecture changes

### Examples
- Fix sleep current issue: 1.0.0 → 1.0.1
- Add new sensor support: 1.0.1 → 1.1.0
- Change API interface: 1.1.0 → 2.0.0

## Commit Message Format

### Standard Format
```
[Version X.X.X] Brief description

- Detailed change 1
- Detailed change 2
- Power impact: [Increased/Decreased/No change]
- Testing: [Pass/Fail with details]
```

### Example
```
[Version 1.0.1] Fix excessive sleep current issue

- Configured PA15 as INPUT_PULLUP (was floating)
- Disabled internal pullup on PA9 (conflicts with external)
- Power impact: Decreased sleep from 15µA to 2.8µA
- Testing: Pass - 48hr stability test completed
```

## Emergency Rollback Procedure

If a version introduces critical issues:

1. **Immediate Actions**
   - [ ] Document the issue in CHANGELOG.md
   - [ ] Revert to previous VERSION number
   - [ ] Restore previous code version
   - [ ] Add "REVERTED" note to bad version in CHANGELOG

2. **Recovery Steps**
   - [ ] Identify root cause
   - [ ] Create fix in new version
   - [ ] Extended testing before release
   - [ ] Document lessons learned

## Automated Checks (Future Implementation)

### Power Consumption Validation
```cpp
#ifdef DEBUG
  if (sleepCurrent > 3.0) {
    ERROR("Sleep current exceeds target!");
  }
#endif
```

### Version Consistency Check
```cpp
#define FIRMWARE_VERSION "1.0.0"
static_assert(VERSION_FILE == FIRMWARE_VERSION);
```

## Critical Reminders

1. **NEVER** skip version updates - it's automatic, not optional
2. **ALWAYS** test power consumption after changes
3. **DOCUMENT** everything - future agents depend on it
4. **VERIFY** all unused pins are configured
5. **MEASURE** before and after power consumption

## Review Questions Before Completion

1. Did you increment the version?
2. Is the CHANGELOG updated?
3. Are all documentation files current?
4. Does the code still meet power targets?
5. Have you removed all debug code?
6. Are all pins properly configured?
7. Is the version consistent everywhere?

If any answer is "No", the task is not complete.

---
*This checklist is mandatory for all code changes. No exceptions.*
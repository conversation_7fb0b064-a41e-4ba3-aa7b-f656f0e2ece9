/**
 * Serial Number Generator - COLDRA Factory Tool
 * Generates unique serial numbers in format: DDMMYY-R-NNN
 * Where: DD=day, MM=month, YY=year, R=region code, NNN=counter
 */

const logger = require('./logger');

class SerialNumberGenerator {
    constructor(dbConnection) {
        this.db = dbConnection.getDatabase();
        this.regionCodes = {
            'US915': 'U',
            'EU868': 'E', 
            'IN865': 'I'
        };
    }

    // Generate next serial number for given region
    async generateSerialNumber(region = 'US915') {
        try {
            // Validate region
            if (!this.regionCodes[region]) {
                throw new Error(`Invalid region: ${region}. Supported: ${Object.keys(this.regionCodes).join(', ')}`);
            }

            const today = new Date();
            const dateStr = this.formatDate(today);
            const regionCode = this.regionCodes[region];

            // Get or create counter for today and region
            const counter = await this.getNextCounter(dateStr, region);
            
            // Format: DDMMYY-R-NNN
            const serialNumber = `${dateStr}-${regionCode}-${counter.toString().padStart(3, '0')}`;

            logger.info('Serial number generated', {
                serial_number: serialNumber,
                region: region,
                date: dateStr,
                counter: counter
            });

            return {
                serial_number: serialNumber,
                region: region,
                date: dateStr,
                counter: counter,
                region_code: regionCode,
                generated_at: new Date().toISOString()
            };

        } catch (error) {
            logger.error('Serial number generation failed', error);
            throw error;
        }
    }

    // Format date as DDMMYY
    formatDate(date) {
        const day = date.getDate().toString().padStart(2, '0');
        const month = (date.getMonth() + 1).toString().padStart(2, '0');
        const year = date.getFullYear().toString().slice(-2);
        return `${day}${month}${year}`;
    }

    // Get next counter for date and region
    async getNextCounter(dateStr, region) {
        return new Promise((resolve, reject) => {
            // Start transaction
            this.db.serialize(() => {
                this.db.run('BEGIN TRANSACTION');

                // Get current counter
                this.db.get(
                    'SELECT counter FROM serial_counters WHERE date = ? AND region = ?',
                    [dateStr, region],
                    (err, row) => {
                        if (err) {
                            this.db.run('ROLLBACK');
                            reject(err);
                            return;
                        }

                        let nextCounter = 1;
                        
                        if (row) {
                            // Increment existing counter
                            nextCounter = row.counter + 1;
                            this.db.run(
                                'UPDATE serial_counters SET counter = ? WHERE date = ? AND region = ?',
                                [nextCounter, dateStr, region],
                                (updateErr) => {
                                    if (updateErr) {
                                        this.db.run('ROLLBACK');
                                        reject(updateErr);
                                    } else {
                                        this.db.run('COMMIT');
                                        resolve(nextCounter);
                                    }
                                }
                            );
                        } else {
                            // Create new counter entry
                            this.db.run(
                                'INSERT INTO serial_counters (date, region, counter) VALUES (?, ?, ?)',
                                [dateStr, region, nextCounter],
                                (insertErr) => {
                                    if (insertErr) {
                                        this.db.run('ROLLBACK');
                                        reject(insertErr);
                                    } else {
                                        this.db.run('COMMIT');
                                        resolve(nextCounter);
                                    }
                                }
                            );
                        }
                    }
                );
            });
        });
    }

    // Validate serial number format
    validateSerialNumber(serialNumber) {
        const pattern = /^(\d{6})-([UEI])-(\d{3})$/;
        const match = serialNumber.match(pattern);
        
        if (!match) {
            return {
                valid: false,
                error: 'Invalid format. Expected: DDMMYY-R-NNN'
            };
        }

        const [, dateStr, regionCode, counterStr] = match;
        
        // Validate date
        const day = parseInt(dateStr.slice(0, 2));
        const month = parseInt(dateStr.slice(2, 4));
        const year = parseInt('20' + dateStr.slice(4, 6));
        
        if (day < 1 || day > 31 || month < 1 || month > 12) {
            return {
                valid: false,
                error: 'Invalid date in serial number'
            };
        }

        // Validate region code
        const region = Object.keys(this.regionCodes).find(r => this.regionCodes[r] === regionCode);
        if (!region) {
            return {
                valid: false,
                error: 'Invalid region code'
            };
        }

        return {
            valid: true,
            parsed: {
                date: dateStr,
                region: region,
                region_code: regionCode,
                counter: parseInt(counterStr),
                full_date: new Date(year, month - 1, day)
            }
        };
    }

    // Get serial number statistics
    async getStatistics(date = null) {
        return new Promise((resolve, reject) => {
            let query = 'SELECT region, counter FROM serial_counters';
            let params = [];

            if (date) {
                query += ' WHERE date = ?';
                params.push(date);
            }

            query += ' ORDER BY date DESC, region';

            this.db.all(query, params, (err, rows) => {
                if (err) {
                    reject(err);
                    return;
                }

                const stats = {
                    total_generated: 0,
                    by_region: {},
                    date_filter: date || 'all_dates'
                };

                rows.forEach(row => {
                    stats.total_generated += row.counter;
                    stats.by_region[row.region] = (stats.by_region[row.region] || 0) + row.counter;
                });

                resolve(stats);
            });
        });
    }

    // Check if serial number exists in devices table
    async checkSerialNumberExists(serialNumber) {
        return new Promise((resolve, reject) => {
            this.db.get(
                'SELECT id FROM devices WHERE serial_number = ?',
                [serialNumber],
                (err, row) => {
                    if (err) {
                        reject(err);
                    } else {
                        resolve(!!row);
                    }
                }
            );
        });
    }
}

module.exports = SerialNumberGenerator;

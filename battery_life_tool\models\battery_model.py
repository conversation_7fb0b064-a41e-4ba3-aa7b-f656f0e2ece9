"""
Energizer L91 Battery Model
===========================

This module contains the battery model for Energizer L91 AA Lithium batteries
based on real datasheet curves for temperature and load effects.

Data extracted from: Energizer L91 Datasheet (Form No. L91GL1222)
- Temperature vs Capacity curve (Page 1)
- Load vs Capacity curve (Page 1)
- Voltage discharge profiles (Page 2)
"""

import numpy as np
from scipy.interpolate import interp1d
from typing import Dict, Tuple, Optional
import logging

logger = logging.getLogger(__name__)


class EnergizeL91BatteryModel:
    """
    Battery model for Energizer L91 AA Lithium batteries (2x in series = 3.0V)
    
    Based on real datasheet curves with temperature and load derating effects.
    """
    
    def __init__(self):
        """Initialize the battery model with datasheet curves."""
        
        # Temperature vs Capacity data (extracted from datasheet graph)
        # Temperature in Celsius, Capacity in mAh for different load currents
        self.temp_capacity_data = {
            # 25mA load curve (typical for IoT applications)
            25: {
                -40: 1000,  # mAh at -40°C
                -30: 1400,  # mAh at -30°C
                -20: 2000,  # mAh at -20°C
                -10: 2600,  # mAh at -10°C
                  0: 2900,  # mAh at 0°C
                 10: 3000,  # mAh at 10°C
                 20: 3000,  # mAh at 20°C (nominal)
                 30: 2980,  # mAh at 30°C
                 40: 2950,  # mAh at 40°C
                 50: 2900,  # mAh at 50°C
                 60: 2850   # mAh at 60°C
            },
            # 250mA load curve (high current applications)
            250: {
                -40: 200,   # mAh at -40°C
                -30: 400,   # mAh at -30°C
                -20: 800,   # mAh at -20°C
                -10: 1400,  # mAh at -10°C
                  0: 2000,  # mAh at 0°C
                 10: 2400,  # mAh at 10°C
                 20: 2600,  # mAh at 20°C
                 30: 2580,  # mAh at 30°C
                 40: 2550,  # mAh at 40°C
                 50: 2500,  # mAh at 50°C
                 60: 2450   # mAh at 60°C
            },
            # 1000mA load curve (very high current)
            1000: {
                -40: 50,    # mAh at -40°C
                -30: 100,   # mAh at -30°C
                -20: 300,   # mAh at -20°C
                -10: 700,   # mAh at -10°C
                  0: 1200,  # mAh at 0°C
                 10: 1600,  # mAh at 10°C
                 20: 1800,  # mAh at 20°C
                 30: 1780,  # mAh at 30°C
                 40: 1750,  # mAh at 40°C
                 50: 1700,  # mAh at 50°C
                 60: 1650   # mAh at 60°C
            }
        }
        
        # Load vs Capacity data at room temperature (20°C)
        # Current in mA, Capacity in mAh
        self.load_capacity_data = {
            1:    3200,  # mAh at 1mA continuous
            5:    3100,  # mAh at 5mA continuous
            10:   3050,  # mAh at 10mA continuous
            25:   3000,  # mAh at 25mA continuous (nominal)
            50:   2850,  # mAh at 50mA continuous
            100:  2600,  # mAh at 100mA continuous
            250:  2200,  # mAh at 250mA continuous
            500:  1600,  # mAh at 500mA continuous
            1000: 1200,  # mAh at 1A continuous
            2000: 800    # mAh at 2A continuous
        }
        
        # Battery specifications
        self.nominal_voltage = 3.0  # Volts (2x 1.5V in series)
        self.nominal_capacity = 3000  # mAh at 25mA, 20°C
        self.min_voltage = 2.0  # Volts (cutoff voltage)
        self.max_voltage = 3.6  # Volts (fresh batteries)
        
        # Create interpolation functions
        self._create_interpolation_functions()
        
        logger.info("Energizer L91 Battery Model initialized")
    
    def _create_interpolation_functions(self):
        """Create interpolation functions for smooth curves."""
        
        # Temperature interpolation for different load currents
        self.temp_interpolators = {}
        for load_current, temp_data in self.temp_capacity_data.items():
            temps = np.array(list(temp_data.keys()))
            capacities = np.array(list(temp_data.values()))
            
            self.temp_interpolators[load_current] = interp1d(
                temps, capacities,
                kind='cubic',
                bounds_error=False,
                fill_value='extrapolate'
            )
        
        # Load interpolation at room temperature
        loads = np.array(list(self.load_capacity_data.keys()))
        capacities = np.array(list(self.load_capacity_data.values()))
        
        self.load_interpolator = interp1d(
            loads, capacities,
            kind='cubic',
            bounds_error=False,
            fill_value='extrapolate'
        )
    
    def get_capacity_at_temperature(self, temperature: float, load_current: float = 25.0) -> float:
        """
        Get battery capacity at specific temperature and load current.
        
        Args:
            temperature: Temperature in Celsius
            load_current: Load current in mA
            
        Returns:
            Battery capacity in mAh
        """
        # Find the closest load current curves for interpolation
        available_loads = list(self.temp_capacity_data.keys())
        available_loads.sort()
        
        if load_current <= available_loads[0]:
            # Use lowest load curve
            return float(self.temp_interpolators[available_loads[0]](temperature))
        elif load_current >= available_loads[-1]:
            # Use highest load curve
            return float(self.temp_interpolators[available_loads[-1]](temperature))
        else:
            # Interpolate between two closest load curves
            lower_load = max([load for load in available_loads if load <= load_current])
            upper_load = min([load for load in available_loads if load >= load_current])
            
            if lower_load == upper_load:
                return float(self.temp_interpolators[lower_load](temperature))
            
            # Linear interpolation between load curves
            lower_capacity = self.temp_interpolators[lower_load](temperature)
            upper_capacity = self.temp_interpolators[upper_load](temperature)
            
            # Interpolation factor
            factor = (load_current - lower_load) / (upper_load - lower_load)
            capacity = lower_capacity + factor * (upper_capacity - lower_capacity)
            
            return float(capacity)
    
    def get_capacity_at_load(self, load_current: float, temperature: float = 20.0) -> float:
        """
        Get battery capacity at specific load current and temperature.
        
        Args:
            load_current: Load current in mA
            temperature: Temperature in Celsius (default: 20°C)
            
        Returns:
            Battery capacity in mAh
        """
        # Get base capacity at room temperature
        base_capacity = float(self.load_interpolator(load_current))
        
        # Apply temperature correction
        if temperature != 20.0:
            # Get temperature factor by comparing capacities
            capacity_at_temp = self.get_capacity_at_temperature(temperature, load_current)
            capacity_at_20c = self.get_capacity_at_temperature(20.0, load_current)
            
            if capacity_at_20c > 0:
                temp_factor = capacity_at_temp / capacity_at_20c
                base_capacity *= temp_factor
        
        return base_capacity
    
    def get_effective_capacity(self, 
                             temperature: float, 
                             average_current: float,
                             peak_current: float = None,
                             duty_cycle: float = 1.0) -> Tuple[float, Dict[str, float]]:
        """
        Calculate effective battery capacity considering all factors.
        
        Args:
            temperature: Operating temperature in Celsius
            average_current: Average current draw in mA
            peak_current: Peak current draw in mA (optional)
            duty_cycle: Duty cycle for peak current (0.0 to 1.0)
            
        Returns:
            Tuple of (effective_capacity_mAh, breakdown_dict)
        """
        breakdown = {}
        
        # Base capacity at temperature and average load
        base_capacity = self.get_capacity_at_temperature(temperature, average_current)
        breakdown['base_capacity_mAh'] = base_capacity
        breakdown['temperature_C'] = temperature
        breakdown['average_current_mA'] = average_current
        
        # Apply peak current penalty if specified
        effective_capacity = base_capacity
        if peak_current and peak_current > average_current:
            # Calculate weighted average current
            weighted_current = average_current * (1 - duty_cycle) + peak_current * duty_cycle
            
            # Get capacity at weighted current
            capacity_at_weighted = self.get_capacity_at_temperature(temperature, weighted_current)
            
            # Apply penalty factor
            peak_penalty = capacity_at_weighted / base_capacity if base_capacity > 0 else 1.0
            effective_capacity *= peak_penalty
            
            breakdown['peak_current_mA'] = peak_current
            breakdown['duty_cycle'] = duty_cycle
            breakdown['weighted_current_mA'] = weighted_current
            breakdown['peak_penalty_factor'] = peak_penalty
        
        breakdown['effective_capacity_mAh'] = effective_capacity
        
        return effective_capacity, breakdown
    
    def estimate_battery_life_hours(self, 
                                  temperature: float,
                                  average_current_mA: float,
                                  peak_current_mA: float = None,
                                  duty_cycle: float = 1.0) -> Tuple[float, Dict[str, float]]:
        """
        Estimate battery life in hours.
        
        Args:
            temperature: Operating temperature in Celsius
            average_current_mA: Average current consumption in mA
            peak_current_mA: Peak current consumption in mA
            duty_cycle: Duty cycle for peak current
            
        Returns:
            Tuple of (battery_life_hours, detailed_breakdown)
        """
        effective_capacity, breakdown = self.get_effective_capacity(
            temperature, average_current_mA, peak_current_mA, duty_cycle
        )
        
        if average_current_mA <= 0:
            return float('inf'), breakdown
        
        # Calculate battery life
        battery_life_hours = effective_capacity / average_current_mA
        breakdown['battery_life_hours'] = battery_life_hours
        breakdown['battery_life_days'] = battery_life_hours / 24
        breakdown['battery_life_years'] = battery_life_hours / (24 * 365.25)
        
        return battery_life_hours, breakdown


def test_battery_model():
    """Test the battery model with known scenarios."""
    battery = EnergizeL91BatteryModel()
    
    print("=== Energizer L91 Battery Model Test ===")
    
    # Test 1: Room temperature, low current
    temp = 20.0
    current = 25.0
    life_hours, breakdown = battery.estimate_battery_life_hours(temp, current)
    print(f"\nTest 1 - Room temp, low current:")
    print(f"Temperature: {temp}°C, Current: {current}mA")
    print(f"Capacity: {breakdown['effective_capacity_mAh']:.0f} mAh")
    print(f"Battery Life: {breakdown['battery_life_days']:.1f} days ({breakdown['battery_life_years']:.2f} years)")
    
    # Test 2: Freezer temperature, same current
    temp = -20.0
    life_hours, breakdown = battery.estimate_battery_life_hours(temp, current)
    print(f"\nTest 2 - Freezer temp, same current:")
    print(f"Temperature: {temp}°C, Current: {current}mA")
    print(f"Capacity: {breakdown['effective_capacity_mAh']:.0f} mAh")
    print(f"Battery Life: {breakdown['battery_life_days']:.1f} days ({breakdown['battery_life_years']:.2f} years)")
    
    # Test 3: Extreme cold
    temp = -40.0
    life_hours, breakdown = battery.estimate_battery_life_hours(temp, current)
    print(f"\nTest 3 - Extreme cold:")
    print(f"Temperature: {temp}°C, Current: {current}mA")
    print(f"Capacity: {breakdown['effective_capacity_mAh']:.0f} mAh")
    print(f"Battery Life: {breakdown['battery_life_days']:.1f} days ({breakdown['battery_life_years']:.2f} years)")


if __name__ == "__main__":
    test_battery_model()

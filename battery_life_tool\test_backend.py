#!/usr/bin/env python3
"""
Test the FastAPI backend server
"""

import requests
import json

def test_backend():
    try:
        # Test health endpoint
        response = requests.get('http://localhost:8001/health')
        print('Health check:', response.json())
        
        # Test calculation endpoint
        test_request = {
            'temperature': 20.0,
            'tx_power_dbm': 14,
            'tx_interval_minutes': 15,
            'spreading_factor': 'SF7',
            'sleep_mode': 'stop2',
            'sensor_resolution_bits': 12,
            'measurements_per_tx': 1,
            'led_enabled': True,
            'battery_count': 2
        }
        
        response = requests.post('http://localhost:8001/calculate-battery-life', json=test_request)
        result = response.json()
        
        print('API Test Result:')
        print(f'  Battery Life: {result["battery_life_years"]:.1f} years')
        print(f'  Daily Energy: {result["daily_energy_mah"]:.3f} mAh')
        print('✅ Backend API working correctly!')
        
        return True
        
    except Exception as e:
        print(f'❌ API Error: {e}')
        return False

if __name__ == "__main__":
    test_backend()

/**
 * COLDRA Factory Tool - Main Server
 * Version: 1.0.0
 *
 * Main entry point for the COLDRA Factory Production Tool
 * A web-based manufacturing system for RAK3172-based LoRaWAN temperature sensors
 */

const express = require('express');
const cors = require('cors');
const helmet = require('helmet');
const morgan = require('morgan');
const path = require('path');

// Initialize Express application
const app = express();
const PORT = process.env.PORT || 3000;

// Security middleware
app.use(helmet({
    contentSecurityPolicy: {
        directives: {
            defaultSrc: ["'self'"],
            styleSrc: ["'self'", "'unsafe-inline'"],
            scriptSrc: ["'self'"],
            imgSrc: ["'self'", "data:", "https:"],
        },
    },
}));

// CORS configuration
app.use(cors({
    origin: process.env.NODE_ENV === 'production' ? false : true,
    credentials: true
}));

// Logging middleware
app.use(morgan('combined'));

// Body parsing middleware
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));

// Serve static files from public directory
app.use(express.static(path.join(__dirname, 'public')));

// Import and use API routes
const apiRoutes = require('./src/routes/api');
app.use('/api', apiRoutes);

// Basic route - Welcome page
app.get('/', (req, res) => {
    res.send(`
        <!DOCTYPE html>
        <html lang="en">
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>COLDRA Factory Tool</title>
            <style>
                body {
                    font-family: Arial, sans-serif;
                    background-color: #006E74;
                    color: #FFFFFF;
                    margin: 0;
                    padding: 40px;
                    text-align: center;
                }
                .container {
                    max-width: 800px;
                    margin: 0 auto;
                    background-color: rgba(0, 151, 172, 0.1);
                    padding: 40px;
                    border-radius: 10px;
                    border: 2px solid #0097AC;
                }
                h1 {
                    color: #0097AC;
                    font-size: 2.5em;
                    margin-bottom: 20px;
                }
                .version {
                    background-color: #0097AC;
                    color: #FFFFFF;
                    padding: 10px 20px;
                    border-radius: 5px;
                    display: inline-block;
                    margin: 20px 0;
                }
                .status {
                    color: #0097AC;
                    font-size: 1.2em;
                    margin: 20px 0;
                }
            </style>
        </head>
        <body>
            <div class="container">
                <h1>🏭 COLDRA Factory Tool</h1>
                <div class="version">Version 1.0.0</div>
                <div class="status">✅ Server Running Successfully</div>
                <p>RAK3172-based LoRaWAN Temperature Sensor Manufacturing System</p>
                <p><strong>Server Status:</strong> Online</p>
                <p><strong>Port:</strong> ${PORT}</p>
                <p><strong>Environment:</strong> Development</p>
                <p><strong>UST Branding:</strong> Applied</p>
                <div style="margin-top: 30px;">
                    <h3 style="color: #0097AC;">API Endpoints Available:</h3>
                    <ul style="text-align: left; display: inline-block;">
                        <li><strong>1.</strong> <a href="/api/health" style="color: #0097AC;">/api/health</a> - System health check</li>
                        <li><strong>2.</strong> <a href="/api/status" style="color: #0097AC;">/api/status</a> - Application status</li>
                        <li><strong>3.</strong> <a href="/api/version" style="color: #0097AC;">/api/version</a> - Version information</li>
                        <li><strong>4.</strong> <a href="/api/test" style="color: #0097AC;">/api/test</a> - Development test endpoint</li>
                        <li><strong>5.</strong> <a href="/api/config/test" style="color: #0097AC;">/api/config/test</a> - Configuration system test</li>
                        <li><strong>6.</strong> <a href="/api/logs/test" style="color: #0097AC;">/api/logs/test</a> - Logging system test</li>
                        <li><strong>7.</strong> <a href="/api/version/manifest" style="color: #0097AC;">/api/version/manifest</a> - SWE system manifest</li>
                        <li><strong>8.</strong> <a href="/api/database/test" style="color: #0097AC;">/api/database/test</a> - Database connection test</li>
                        <li><strong>9.</strong> <a href="/api/database/schema/check" style="color: #0097AC;">/api/database/schema/check</a> - Check database schema</li>
                        <li><strong>10.</strong> <a href="/api/serial/test" style="color: #0097AC;">/api/serial/test</a> - Serial number generator test</li>
                        <li><strong>11.</strong> <a href="/api/devices/test" style="color: #0097AC;">/api/devices/test</a> - Device record manager test</li>
                        <li><strong>12.</strong> <a href="/api/devices/stats" style="color: #0097AC;">/api/devices/stats</a> - Device production statistics</li>
                        <li><strong>13.</strong> <a href="/api/validation/test" style="color: #0097AC;">/api/validation/test</a> - Data validation system test</li>
                        <li><strong>14.</strong> <a href="/api/migrations/test" style="color: #0097AC;">/api/migrations/test</a> - Database migration system test</li>
                        <li><strong>15.</strong> <a href="/api/migrations/history" style="color: #0097AC;">/api/migrations/history</a> - Database migration history</li>
                        <li><strong>📚</strong> <a href="/api/docs" style="color: #0097AC; font-weight: bold;">/api/docs</a> - Complete API documentation</li>
                    </ul>
                </div>
            </div>
        </body>
        </html>
    `);
});

// 404 handler for API routes
app.use('/api/*', (req, res) => {
    res.status(404).json({
        error: 'API endpoint not found',
        path: req.path,
        method: req.method,
        timestamp: new Date().toISOString()
    });
});

// Global error handler
app.use((err, req, res, next) => {
    console.error('Error occurred:', err);

    // Don't leak error details in production
    const isDevelopment = process.env.NODE_ENV !== 'production';

    res.status(err.status || 500).json({
        error: 'Internal server error',
        message: isDevelopment ? err.message : 'Something went wrong',
        timestamp: new Date().toISOString(),
        ...(isDevelopment && { stack: err.stack })
    });
});

// Start server
app.listen(PORT, () => {
    console.log('🏭 COLDRA Factory Tool Server Starting...');
    console.log(`📡 Server running on http://localhost:${PORT}`);
    console.log(`🎨 UST Branding: Dark Teal (#006E74) & Light Teal (#0097AC)`);
    console.log(`📋 Version: 1.0.0`);
    console.log('✅ Ready for development!');
});

// Graceful shutdown
process.on('SIGTERM', () => {
    console.log('🛑 SIGTERM received, shutting down gracefully...');
    process.exit(0);
});

process.on('SIGINT', () => {
    console.log('🛑 SIGINT received, shutting down gracefully...');
    process.exit(0);
});

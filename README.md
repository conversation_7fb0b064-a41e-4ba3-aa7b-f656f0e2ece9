# COLDRA Factory Tool

**Version:** 1.0.0  
**Status:** Development Phase 1  

## Overview

The COLDRA Factory Production Tool is a comprehensive web-based application designed to automate the manufacturing process of RAK3172-based LoRaWAN temperature sensors.

## Quick Start

### Prerequisites
- Node.js >= 16.0.0
- npm >= 8.0.0

### Installation & Running

1. **Install Dependencies**
   ```bash
   npm install
   ```

2. **Start Development Server**
   ```bash
   npm start
   ```

3. **Open Browser**
   Navigate to: http://localhost:3000

### Expected Output
- Server starts on port 3000
- Welcome page displays with UST branding
- Console shows startup messages

## Project Structure

```
coldra-factory-tool/
├── package.json          # Project configuration
├── server.js            # Main server entry point
├── README.md            # This file
└── public/              # Static files (to be created)
```

## Development Status

### ✅ Completed Tasks
- [x] Task 1.1: Initialize Node.js Project Structure

### 🚧 Current Phase
- Phase 1: Foundation & Project Setup

### 📋 Next Tasks
- Task 1.2: Setup Express.js Backend Framework
- Task 1.3: Create Configuration Management System
- Task 1.4: Setup Logging System
- Task 1.5: Create Version Tracking System
- Task 1.6: Setup Development Documentation

## Testing Task 1.1

### Test Steps:
1. Run `npm install` to install dependencies
2. Run `npm start` to start the server
3. Open browser to http://localhost:3000
4. Verify welcome page displays with UST colors
5. Check console for startup messages

### Expected Results:
- ✅ Server starts without errors
- ✅ Welcome page shows "COLDRA Factory Tool"
- ✅ UST branding colors are applied (Dark Teal #006E74, Light Teal #0097AC)
- ✅ Version 1.0.0 is displayed
- ✅ Console shows startup messages

## UST Brand Colors
- **Dark Teal:** #006E74 (backgrounds)
- **Light Teal:** #0097AC (active elements)
- **White:** #FFFFFF (text on dark)
- **Soft Black:** #231F20 (text on light)

---
**Development Team:** UST  
**Project:** COLDRA Ultra-Low Power Temperature Sensor Manufacturing

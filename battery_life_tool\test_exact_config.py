#!/usr/bin/env python3
"""
Test exact configuration values without temperature effects
"""

import requests
import json

def test_exact_configuration():
    print('=== TESTING EXACT CONFIGURATION VALUES ===')

    # Test with zero temperature coefficients to eliminate temperature effects
    test_config = {
        'lora_config': {
            'tx_current_14dbm': 30.0,
            'sleep_current_stop2': 2.0,
            'tx_temp_coefficient': 0.0,  # No temperature effect
            'sleep_temp_coefficient': 0.0  # No temperature effect
        }
    }

    response = requests.post('http://localhost:8001/save-configuration', json=test_config)
    if response.status_code == 200:
        print('✅ Configuration saved with zero temp coefficients')
        
        # Test calculation at different temperature
        calc_response = requests.post('http://localhost:8001/calculate-battery-life', json={
            'temperature': 20.0,  # Different from 25°C reference
            'tx_power_dbm': 14,
            'tx_interval_minutes': 15,
            'spreading_factor': 'SF7',
            'sleep_mode': 'stop2',
            'sensor_resolution_bits': 12,
            'measurements_per_tx': 1,
            'led_enabled': True,
            'battery_count': 2
        })
        
        if calc_response.status_code == 200:
            calc_result = calc_response.json()
            tx_current = calc_result['lora_details']['tx_current_ma']
            sleep_current = calc_result['lora_details']['sleep_current_ua']
            
            print(f'TX Current at 20°C: {tx_current} mA (should be exactly 30.0)')
            print(f'Sleep Current at 20°C: {sleep_current} µA (should be exactly 2.0)')
            
            if abs(tx_current - 30.0) < 0.01:
                print('✅ TX current matches configured value!')
            else:
                print('❌ TX current still differs from configured value')
                
            if abs(sleep_current - 2.0) < 0.01:
                print('✅ Sleep current matches configured value!')
            else:
                print('❌ Sleep current still differs from configured value')
                
            print(f'Battery life: {calc_result["battery_life_years"]} years')
            
            # Test with different TX power levels
            print('\n=== TESTING DIFFERENT TX POWER LEVELS ===')
            for power in [7, 10, 14, 17, 20]:
                calc_response = requests.post('http://localhost:8001/calculate-battery-life', json={
                    'temperature': 25.0,  # Reference temperature
                    'tx_power_dbm': power,
                    'tx_interval_minutes': 15,
                    'spreading_factor': 'SF7',
                    'sleep_mode': 'stop2',
                    'sensor_resolution_bits': 12,
                    'measurements_per_tx': 1,
                    'led_enabled': True,
                    'battery_count': 2
                })
                
                if calc_response.status_code == 200:
                    result = calc_response.json()
                    tx_current = result['lora_details']['tx_current_ma']
                    print(f'{power} dBm: {tx_current} mA')
                    
        else:
            print('❌ Calculation failed')
            print(f'Status: {calc_response.status_code}')
            print(f'Response: {calc_response.text}')
    else:
        print('❌ Configuration save failed')
        print(f'Status: {response.status_code}')
        print(f'Response: {response.text}')

if __name__ == "__main__":
    test_exact_configuration()

#!/usr/bin/env python3
"""
Test the updated API with detailed breakdown
"""

import requests
import json

def test_detailed_api():
    try:
        test_data = {
            'temperature': -20.0,
            'tx_power_dbm': 14,
            'tx_interval_minutes': 15,
            'spreading_factor': 'SF7'
        }
        
        response = requests.post('http://localhost:8001/calculate-battery-life', json=test_data)
        result = response.json()
        
        print('=== UPDATED API RESPONSE TEST ===')
        print(f'Battery Life: {result["battery_life_years"]} years')
        print(f'Daily Energy: {result["daily_energy_mah"]} mAh')
        
        print('\n=== LORA DETAILS ===')
        lora = result['lora_details']
        print(f'TX Current: {lora["tx_current_ma"]} mA')
        print(f'Sleep Current: {lora["sleep_current_ua"]} µA')
        print(f'TX Time: {lora["tx_time_ms"]} ms')
        print(f'Sleep Time: {lora["sleep_time_hours"]} hours/day')
        print(f'Transmissions per day: {lora["tx_per_day"]}')
        
        print('\n=== SENSOR DETAILS ===')
        sensor = result['sensor_details']
        print(f'Active Current: {sensor["active_current_ma"]} mA')
        print(f'Standby Current: {sensor["standby_current_ua"]} µA')
        print(f'Conversion Time: {sensor["conversion_time_ms"]} ms')
        print(f'Standby Time: {sensor["standby_time_hours"]} hours/day')
        print(f'Measurements per day: {sensor["measurements_per_day"]}')
        
        print('\n✅ Updated API working with detailed breakdown!')
        return True
        
    except Exception as e:
        print(f'❌ API Error: {e}')
        return False

if __name__ == "__main__":
    test_detailed_api()

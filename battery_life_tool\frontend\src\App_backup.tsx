import React, { useState, useEffect } from 'react';
import axios from 'axios';
import './App.css';

interface BatteryLifeRequest {
  temperature: number;
  tx_power_dbm: number;
  tx_interval_minutes: number;
  spreading_factor: string;
  sleep_mode: string;
  sensor_resolution_bits: number;
  measurements_per_tx: number;
  led_enabled: boolean;
  battery_count: number;
}

interface BatteryLifeResponse {
  battery_life_days: number;
  battery_life_years: number;
  daily_energy_mah: number;
  energy_breakdown: {
    lora_percent: number;
    sensor_percent: number;
    led_percent: number;
  };
  battery_info: {
    effective_capacity_mah: number;
    average_current_ma: number;
  };
  lora_details: {
    tx_current_ma: number;
    sleep_current_ua: number;
    tx_time_ms: number;
    sleep_time_hours: number;
    tx_per_day: number;
  };
  sensor_details: {
    active_current_ma: number;
    standby_current_ua: number;
    conversion_time_ms: number;
    standby_time_hours: number;
    measurements_per_day: number;
  };
  confidence_level: number;
  confidence_description: string;
  input_parameters: BatteryLifeRequest;
}

function App() {
  // State for input parameters
  const [temperature, setTemperature] = useState<number>(20);
  const [txPower, setTxPower] = useState<number>(14);
  const [txInterval, setTxInterval] = useState<number>(15);
  const [spreadingFactor, setSpreadingFactor] = useState<string>('SF7');

  // State for results
  const [result, setResult] = useState<BatteryLifeResponse | null>(null);
  const [loading, setLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);

  // State for configuration interface
  const [showConfig, setShowConfig] = useState<boolean>(false);
  const [activeConfigTab, setActiveConfigTab] = useState<string>('lora');
  const [configuration, setConfiguration] = useState<any>(null);
  const [configLoading, setConfigLoading] = useState<boolean>(false);
  const [configSaving, setConfigSaving] = useState<boolean>(false);
  const [configValues, setConfigValues] = useState<any>({});

  // API endpoint
  const API_BASE_URL = 'http://localhost:8001';

  // Calculate battery life
  const calculateBatteryLife = async () => {
    setLoading(true);
    setError(null);

    const request: BatteryLifeRequest = {
      temperature,
      tx_power_dbm: txPower,
      tx_interval_minutes: txInterval,
      spreading_factor: spreadingFactor,
      sleep_mode: 'stop2',
      sensor_resolution_bits: 12,
      measurements_per_tx: 1,
      led_enabled: true,
      battery_count: 2
    };

    try {
      const response = await axios.post<BatteryLifeResponse>(
        `${API_BASE_URL}/calculate-battery-life`,
        request
      );
      setResult(response.data);
    } catch (err: any) {
      setError(err.response?.data?.detail || 'Calculation failed');
      console.error('API Error:', err);
    } finally {
      setLoading(false);
    }
  };

  // Load configuration from backend
  const loadConfiguration = async () => {
    setConfigLoading(true);
    try {
      const response = await axios.get(`${API_BASE_URL}/get-configuration`);
      setConfiguration(response.data.configuration);
    } catch (err: any) {
      console.error('Failed to load configuration:', err);
      setError('Failed to load configuration');
    } finally {
      setConfigLoading(false);
    }
  };

  // Save configuration to backend
  const saveConfiguration = async (configUpdates: any) => {
    setConfigSaving(true);
    try {
      const response = await axios.post(`${API_BASE_URL}/save-configuration`, configUpdates);
      setConfiguration(response.data.configuration);

      // Recalculate battery life with new configuration
      await calculateBatteryLife();

      alert('✅ Configuration saved successfully! Battery life recalculated with new parameters.');
    } catch (err: any) {
      console.error('Failed to save configuration:', err);
      alert('❌ Failed to save configuration: ' + (err.response?.data?.detail || err.message));
    } finally {
      setConfigSaving(false);
    }
  };

  // Reset configuration to defaults
  const resetConfiguration = async () => {
    if (!confirm('Are you sure you want to reset all parameters to datasheet defaults?')) {
      return;
    }

    setConfigSaving(true);
    try {
      const response = await axios.post(`${API_BASE_URL}/reset-configuration`);
      setConfiguration(response.data.configuration);

      // Recalculate battery life with default configuration
      await calculateBatteryLife();

      alert('✅ Configuration reset to defaults! Battery life recalculated.');
    } catch (err: any) {
      console.error('Failed to reset configuration:', err);
      alert('❌ Failed to reset configuration: ' + (err.response?.data?.detail || err.message));
    } finally {
      setConfigSaving(false);
    }
  };

  // Auto-calculate when inputs change (debounced)
  useEffect(() => {
    const timer = setTimeout(() => {
      calculateBatteryLife();
    }, 500);

    return () => clearTimeout(timer);
  }, [temperature, txPower, txInterval, spreadingFactor]);

  // Handle configuration value changes
  const handleConfigChange = (section: string, field: string, value: any) => {
    setConfigValues((prev: any) => ({
      ...prev,
      [section]: {
        ...prev[section],
        [field]: value
      }
    }));
  };

  // Save configuration with collected form values
  const handleSaveConfiguration = async () => {
    if (Object.keys(configValues).length === 0) {
      alert('⚠️ No changes to save. Modify some parameters first.');
      return;
    }

    await saveConfiguration(configValues);
    setConfigValues({}); // Clear pending changes
  };

  // Load configuration on component mount
  useEffect(() => {
    loadConfiguration();
  }, []);

  // Update configValues when configuration is loaded
  useEffect(() => {
    if (configuration) {
      setConfigValues({});
    }
  }, [configuration]);

  return (
    <div className="App">
      <header className="App-header">
        <h1>COLDRA Battery Life Assessment Tool</h1>
        <p>LoRaWAN Temperature Sensor Battery Life Calculator</p>
        <div className="header-buttons">
          <button
            className="config-button"
            onClick={() => setShowConfig(!showConfig)}
          >
            ⚙️ Configure Parameters
          </button>
        </div>
      </header>

      <main className="App-main">
        <div className="calculator-container">
          {/* Input Controls */}
          <div className="input-section">
            <h2>Configuration</h2>

            <div className="input-group">
              <label htmlFor="temperature">
                Temperature: {temperature}°C
              </label>
              <input
                id="temperature"
                type="range"
                min="-40"
                max="40"
                value={temperature}
                onChange={(e) => setTemperature(Number(e.target.value))}
                className="slider"
              />
              <div className="range-labels">
                <span>-40°C</span>
                <span>+40°C</span>
              </div>
            </div>

            <div className="input-group">
              <label htmlFor="txPower">
                TX Power: {txPower} dBm
              </label>
              <select
                id="txPower"
                value={txPower}
                onChange={(e) => setTxPower(Number(e.target.value))}
                className="select"
              >
                <option value={7}>7 dBm (Low Power)</option>
                <option value={10}>10 dBm</option>
                <option value={14}>14 dBm (Medium)</option>
                <option value={17}>17 dBm</option>
                <option value={20}>20 dBm (High Power)</option>
                <option value={22}>22 dBm (Max Power)</option>
              </select>
            </div>

            <div className="input-group">
              <label htmlFor="txInterval">
                TX Interval: {txInterval} minutes
              </label>
              <input
                id="txInterval"
                type="number"
                min="1"
                max="1440"
                value={txInterval}
                onChange={(e) => setTxInterval(Number(e.target.value))}
                className="number-input"
              />
            </div>

            <div className="input-group">
              <label htmlFor="spreadingFactor">
                Spreading Factor: {spreadingFactor}
              </label>
              <select
                id="spreadingFactor"
                value={spreadingFactor}
                onChange={(e) => setSpreadingFactor(e.target.value)}
                className="select"
              >
                <option value="SF7">SF7 (Fast, Short Range)</option>
                <option value="SF8">SF8</option>
                <option value="SF9">SF9</option>
                <option value="SF10">SF10</option>
                <option value="SF11">SF11</option>
                <option value="SF12">SF12 (Slow, Long Range)</option>
              </select>
            </div>
          </div>

          {/* Results Display */}
          <div className="results-section">
            <h2>Battery Life Prediction</h2>

            {loading && (
              <div className="loading">
                <p>Calculating...</p>
              </div>
            )}

            {error && (
              <div className="error">
                <p>Error: {error}</p>
              </div>
            )}

            {result && !loading && (
              <div className="results">
                <div className="main-result">
                  <h3>{result.battery_life_years.toFixed(1)} Years</h3>
                  <p>{result.battery_life_days.toFixed(0)} days</p>
                </div>

                <div className="details">
                  <div className="detail-item">
                    <strong>Daily Energy:</strong> {result.daily_energy_mah.toFixed(3)} mAh
                  </div>

                  <div className="detail-item">
                    <strong>Confidence:</strong> {result.confidence_description} ({(result.confidence_level * 100).toFixed(0)}%)
                  </div>

                  <div className="energy-breakdown">
                    <h4>Energy Distribution:</h4>
                    <div className="breakdown-item">
                      <span>LoRa Module:</span>
                      <span>{result.energy_breakdown.lora_percent.toFixed(1)}%</span>
                    </div>
                    <div className="breakdown-item">
                      <span>Temperature Sensor:</span>
                      <span>{result.energy_breakdown.sensor_percent.toFixed(1)}%</span>
                    </div>
                    <div className="breakdown-item">
                      <span>LED Indicator:</span>
                      <span>{result.energy_breakdown.led_percent.toFixed(1)}%</span>
                    </div>
                  </div>

                  <div className="battery-info">
                    <h4>Battery Information:</h4>
                    <div className="breakdown-item">
                      <span>Effective Capacity:</span>
                      <span>{result.battery_info.effective_capacity_mah.toFixed(0)} mAh</span>
                    </div>
                    <div className="breakdown-item">
                      <span>Average Current:</span>
                      <span>{result.battery_info.average_current_ma.toFixed(3)} mA</span>
                    </div>
                  </div>

                  <div className="lora-details">
                    <h4>LoRa Module Details:</h4>
                    <div className="breakdown-item">
                      <span>TX Current:</span>
                      <span>{result.lora_details.tx_current_ma.toFixed(2)} mA</span>
                    </div>
                    <div className="breakdown-item">
                      <span>Sleep Current:</span>
                      <span>{result.lora_details.sleep_current_ua.toFixed(2)} µA</span>
                    </div>
                    <div className="breakdown-item">
                      <span>TX Time:</span>
                      <span>{result.lora_details.tx_time_ms.toFixed(1)} ms</span>
                    </div>
                    <div className="breakdown-item">
                      <span>Sleep Time:</span>
                      <span>{result.lora_details.sleep_time_hours.toFixed(2)} hrs/day</span>
                    </div>
                    <div className="breakdown-item">
                      <span>Transmissions/Day:</span>
                      <span>{result.lora_details.tx_per_day}</span>
                    </div>
                  </div>

                  <div className="sensor-details">
                    <h4>Temperature Sensor Details:</h4>
                    <div className="breakdown-item">
                      <span>Active Current:</span>
                      <span>{result.sensor_details.active_current_ma.toFixed(2)} mA</span>
                    </div>
                    <div className="breakdown-item">
                      <span>Standby Current:</span>
                      <span>{result.sensor_details.standby_current_ua.toFixed(2)} µA</span>
                    </div>
                    <div className="breakdown-item">
                      <span>Conversion Time:</span>
                      <span>{result.sensor_details.conversion_time_ms.toFixed(0)} ms</span>
                    </div>
                    <div className="breakdown-item">
                      <span>Standby Time:</span>
                      <span>{result.sensor_details.standby_time_hours.toFixed(2)} hrs/day</span>
                    </div>
                    <div className="breakdown-item">
                      <span>Measurements/Day:</span>
                      <span>{result.sensor_details.measurements_per_day}</span>
                    </div>
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>
      </main>

      {/* Configuration Modal */}
      {showConfig && (
        <div className="config-modal-overlay">
          <div className="config-modal">
            <div className="config-header">
              <h2>⚙️ Parameter Configuration</h2>
              <p className="config-warning">
                ⚠️ <strong>IMPORTANT:</strong> These parameters should be measured by firmware team,
                not assumed from datasheets!
              </p>
              <button
                className="close-button"
                onClick={() => setShowConfig(false)}
              >
                ✕
              </button>
            </div>

            <div className="config-tabs">
              <button
                className={`tab-button ${activeConfigTab === 'lora' ? 'active' : ''}`}
                onClick={() => setActiveConfigTab('lora')}
              >
                LoRa Module
              </button>
              <button
                className={`tab-button ${activeConfigTab === 'sensor' ? 'active' : ''}`}
                onClick={() => setActiveConfigTab('sensor')}
              >
                Temperature Sensor
              </button>
              <button
                className={`tab-button ${activeConfigTab === 'system' ? 'active' : ''}`}
                onClick={() => setActiveConfigTab('system')}
              >
                System Level
              </button>
              <button
                className={`tab-button ${activeConfigTab === 'battery' ? 'active' : ''}`}
                onClick={() => setActiveConfigTab('battery')}
              >
                Battery
              </button>
              <button
                className={`tab-button ${activeConfigTab === 'firmware' ? 'active' : ''}`}
                onClick={() => setActiveConfigTab('firmware')}
              >
                Firmware
              </button>
            </div>

            <div className="config-content">
              {activeConfigTab === 'lora' && (
                <div className="config-section">
                  <h3>LoRa Module Parameters</h3>
                  <p className="section-note">
                    🔬 <strong>MEASURE THESE WITH REAL FIRMWARE:</strong> Datasheet values are theoretical!
                  </p>

                  <div className="config-group">
                    <h4>TX Current Consumption (mA)</h4>
                    <div className="config-grid">
                      <label>7 dBm:</label>
                      <input
                        type="number"
                        step="0.1"
                        value={configValues.lora_config?.tx_current_7dbm ?? configuration?.lora_config?.tx_current_7dbm ?? 15.0}
                        onChange={(e) => handleConfigChange('lora_config', 'tx_current_7dbm', parseFloat(e.target.value))}
                      />
                      <label>14 dBm:</label>
                      <input
                        type="number"
                        step="0.1"
                        value={configValues.lora_config?.tx_current_14dbm ?? configuration?.lora_config?.tx_current_14dbm ?? 24.0}
                        onChange={(e) => handleConfigChange('lora_config', 'tx_current_14dbm', parseFloat(e.target.value))}
                      />
                      <label>20 dBm:</label>
                      <input
                        type="number"
                        step="0.1"
                        value={configValues.lora_config?.tx_current_20dbm ?? configuration?.lora_config?.tx_current_20dbm ?? 87.0}
                        onChange={(e) => handleConfigChange('lora_config', 'tx_current_20dbm', parseFloat(e.target.value))}
                      />
                    </div>
                  </div>

                  <div className="config-group">
                    <h4>Sleep Current Consumption (µA)</h4>
                    <div className="config-grid">
                      <label>Stop2 Mode:</label>
                      <input
                        type="number"
                        step="0.01"
                        value={configValues.lora_config?.sleep_current_stop2 ?? configuration?.lora_config?.sleep_current_stop2 ?? 1.69}
                        onChange={(e) => handleConfigChange('lora_config', 'sleep_current_stop2', parseFloat(e.target.value))}
                      />
                      <label>Stop1 Mode:</label>
                      <input
                        type="number"
                        step="0.01"
                        value={configValues.lora_config?.sleep_current_stop1 ?? configuration?.lora_config?.sleep_current_stop1 ?? 2.1}
                        onChange={(e) => handleConfigChange('lora_config', 'sleep_current_stop1', parseFloat(e.target.value))}
                      />
                    </div>
                  </div>

                  <div className="config-group">
                    <h4>Timing Parameters</h4>
                    <div className="config-grid">
                      <label>Wake-up Time (ms):</label>
                      <input
                        type="number"
                        step="0.1"
                        value={configValues.lora_config?.wakeup_time_ms ?? configuration?.lora_config?.wakeup_time_ms ?? 5.0}
                        onChange={(e) => handleConfigChange('lora_config', 'wakeup_time_ms', parseFloat(e.target.value))}
                      />
                      <label>Boot Time (ms):</label>
                      <input
                        type="number"
                        step="1"
                        value={configValues.lora_config?.boot_time_ms ?? configuration?.lora_config?.boot_time_ms ?? 100}
                        onChange={(e) => handleConfigChange('lora_config', 'boot_time_ms', parseFloat(e.target.value))}
                      />
                    </div>
                  </div>
                </div>
              )}

              {activeConfigTab === 'sensor' && (
                <div className="config-section">
                  <h3>Temperature Sensor Parameters</h3>
                  <p className="section-note">
                    🔬 <strong>MEASURE THESE WITH REAL FIRMWARE:</strong> Actual values may differ!
                  </p>

                  <div className="config-group">
                    <h4>Current Consumption</h4>
                    <div className="config-grid">
                      <label>Active Current (mA):</label>
                      <input
                        type="number"
                        step="0.01"
                        value={configValues.sensor_config?.active_current_ma ?? configuration?.sensor_config?.active_current_ma ?? 1.5}
                        onChange={(e) => handleConfigChange('sensor_config', 'active_current_ma', parseFloat(e.target.value))}
                      />
                      <label>Standby Current (µA):</label>
                      <input
                        type="number"
                        step="0.01"
                        value={configValues.sensor_config?.standby_current_ua ?? configuration?.sensor_config?.standby_current_ua ?? 0.75}
                        onChange={(e) => handleConfigChange('sensor_config', 'standby_current_ua', parseFloat(e.target.value))}
                      />
                    </div>
                  </div>

                  <div className="config-group">
                    <h4>Timing Parameters</h4>
                    <div className="config-grid">
                      <label>12-bit Conversion (ms):</label>
                      <input
                        type="number"
                        step="1"
                        value={configValues.sensor_config?.conversion_time_12bit_ms ?? configuration?.sensor_config?.conversion_time_12bit_ms ?? 750}
                        onChange={(e) => handleConfigChange('sensor_config', 'conversion_time_12bit_ms', parseFloat(e.target.value))}
                      />
                      <label>11-bit Conversion (ms):</label>
                      <input
                        type="number"
                        step="1"
                        value={configValues.sensor_config?.conversion_time_11bit_ms ?? configuration?.sensor_config?.conversion_time_11bit_ms ?? 375}
                        onChange={(e) => handleConfigChange('sensor_config', 'conversion_time_11bit_ms', parseFloat(e.target.value))}
                      />
                    </div>
                  </div>
                </div>
              )}

              {activeConfigTab === 'system' && (
                <div className="config-section">
                  <h3>System Level Parameters</h3>
                  <p className="section-note">
                    ⚠️ <strong>OFTEN MISSED:</strong> These parameters are frequently overlooked but critical!
                  </p>

                  <div className="config-group">
                    <h4>Additional Current Consumers</h4>
                    <div className="config-grid">
                      <label>RTC Current (µA):</label>
                      <input type="number" step="0.1" defaultValue="1.0" />
                      <label>Watchdog (µA):</label>
                      <input type="number" step="0.1" defaultValue="0.5" />
                      <label>GPIO Leakage (µA):</label>
                      <input type="number" step="0.1" defaultValue="0.1" />
                      <label>PCB Leakage (µA):</label>
                      <input type="number" step="0.1" defaultValue="0.5" />
                      <label>Regulator Quiescent (µA):</label>
                      <input type="number" step="0.1" defaultValue="10.0" />
                    </div>
                  </div>
                </div>
              )}

              {activeConfigTab === 'battery' && (
                <div className="config-section">
                  <h3>Battery Parameters</h3>
                  <p className="section-note">
                    🔋 <strong>VALIDATE WITH REAL BATTERIES:</strong> Batch variations exist!
                  </p>

                  <div className="config-group">
                    <h4>Basic Parameters</h4>
                    <div className="config-grid">
                      <label>Nominal Capacity (mAh):</label>
                      <input type="number" step="100" defaultValue="3000" />
                      <label>Battery Count:</label>
                      <input type="number" step="1" defaultValue="2" />
                      <label>Cutoff Voltage (V):</label>
                      <input type="number" step="0.1" defaultValue="2.0" />
                    </div>
                  </div>
                </div>
              )}

              {activeConfigTab === 'firmware' && (
                <div className="config-section">
                  <h3>Firmware-Specific Parameters</h3>
                  <p className="section-note">
                    💻 <strong>FIRMWARE DEPENDENT:</strong> These vary based on implementation!
                  </p>

                  <div className="config-group">
                    <h4>Error Handling</h4>
                    <div className="config-grid">
                      <label>Retry Attempts:</label>
                      <input type="number" step="1" defaultValue="3" />
                      <label>Retry Current (mA):</label>
                      <input type="number" step="0.1" defaultValue="25.0" />
                    </div>
                  </div>

                  <div className="config-group">
                    <h4>Features</h4>
                    <div className="config-grid">
                      <label>Debug Enabled:</label>
                      <input type="checkbox" defaultChecked={false} />
                      <label>Encryption Enabled:</label>
                      <input type="checkbox" defaultChecked={true} />
                    </div>
                  </div>
                </div>
              )}
            </div>

            <div className="config-footer">
              <button
                className="save-config-button"
                onClick={handleSaveConfiguration}
                disabled={configSaving}
              >
                {configSaving ? '💾 Saving...' : '💾 Save Configuration'}
              </button>
              <button
                className="reset-config-button"
                onClick={resetConfiguration}
                disabled={configSaving}
              >
                🔄 Reset to Defaults
              </button>
            </div>
          </div>
        </div>
      )}

      <footer className="App-footer">
        <p>COLDRA Battery Life Assessment Tool v1.0.0</p>
        <p>⚠️ Configure real measured parameters - don't rely on datasheet values!</p>
      </footer>
    </div>
  );
}

export default App;

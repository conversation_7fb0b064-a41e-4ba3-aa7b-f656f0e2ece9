import React, { useState } from 'react';

const Production = () => {
  const [selectedDevice, setSelectedDevice] = useState('');
  const [workflowStep, setWorkflowStep] = useState('select');

  const workflowSteps = [
    { id: 'select', name: '1. Select Device', icon: '📱', status: 'current' },
    { id: 'program', name: '2. Program Firmware', icon: '💾', status: 'pending' },
    { id: 'provision', name: '3. Provision Credentials', icon: '🔐', status: 'pending' },
    { id: 'test', name: '4. Test Device', icon: '🧪', status: 'pending' }
  ];

  return (
    <div className="production">
      <div className="page-header">
        <h1>🏭 Production Workflow</h1>
        <p>Complete device manufacturing process from programming to testing</p>
      </div>

      <div className="workflow-progress">
        <div className="progress-steps">
          {workflowSteps.map((step, index) => (
            <div key={step.id} className={`progress-step ${step.status}`}>
              <div className="step-icon">{step.icon}</div>
              <div className="step-name">{step.name}</div>
              {index < workflowSteps.length - 1 && <div className="step-connector"></div>}
            </div>
          ))}
        </div>
      </div>

      <div className="workflow-content">
        {workflowStep === 'select' && (
          <div className="card">
            <div className="card-title">📱 Select Device for Production</div>
            <div className="device-selector">
              <div className="form-group">
                <label className="form-label">Device Serial Number</label>
                <input
                  type="text"
                  className="form-input"
                  placeholder="Enter or scan device serial number"
                  value={selectedDevice}
                  onChange={(e) => setSelectedDevice(e.target.value)}
                />
              </div>
              <div className="form-actions">
                <button 
                  className="btn btn-primary"
                  disabled={!selectedDevice}
                  onClick={() => setWorkflowStep('program')}
                >
                  Start Production Workflow
                </button>
              </div>
            </div>
          </div>
        )}

        {workflowStep === 'program' && (
          <div className="card">
            <div className="card-title">💾 Firmware Programming</div>
            <div className="workflow-step-content">
              <p>Programming firmware for device: <strong>{selectedDevice}</strong></p>
              <div className="step-actions">
                <button className="btn btn-primary">Start Programming</button>
                <button className="btn btn-secondary" onClick={() => setWorkflowStep('select')}>
                  Back to Selection
                </button>
              </div>
            </div>
          </div>
        )}
      </div>

      <div className="card">
        <div className="card-title">🔧 Production Workflow Endpoints (4 endpoints)</div>
        <div className="endpoints-list">
          <div className="endpoint-item">
            <span className="endpoint-number">17.</span>
            <span className="endpoint-name">POST /api/devices/:id/program</span>
            <span className="endpoint-desc">Start device firmware programming</span>
          </div>
          <div className="endpoint-item">
            <span className="endpoint-number">18.</span>
            <span className="endpoint-name">POST /api/devices/:id/provision</span>
            <span className="endpoint-desc">Start device credential provisioning</span>
          </div>
          <div className="endpoint-item">
            <span className="endpoint-number">19.</span>
            <span className="endpoint-name">POST /api/devices/:id/test</span>
            <span className="endpoint-desc">Start device testing</span>
          </div>
          <div className="endpoint-item">
            <span className="endpoint-number">20.</span>
            <span className="endpoint-name">GET /api/devices/:id/status</span>
            <span className="endpoint-desc">Get current device status</span>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Production;

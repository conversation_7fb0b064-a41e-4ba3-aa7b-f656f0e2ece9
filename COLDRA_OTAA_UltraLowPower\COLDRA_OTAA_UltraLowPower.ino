/**
 * COLDRA Ultra-Low Power Temperature Sensor - OTAA Variant
 * Target: RAK3172 with RUI3 API
 * Power Goal: 2-3µA deep sleep, 15+ year battery life
 * 
 * @version 2.2.0
 * @date 2025-09-06
 * 
 * CRITICAL: This firmware achieves ultra-low power through:
 * - Event-driven architecture (no loop)
 * - Proper LPM configuration
 * - All pins configured to prevent floating
 * - Timer-based wake cycles
 * - STATE MACHINE temperature reading (no blocking delays)
 * 
 * POWER OPTIMIZATION STRATEGY:
 * - OneWire communication: ~2ms total (delayMicroseconds kept to minimum)
 * - Temperature conversion: 750ms sleep period (system can deep sleep during this)
 * - LED flashes: <1ms on-time each
 * - Total active time per cycle: <5 seconds
 * - Expected sleep current: 2-3µA
 * - Expected battery life: 15+ years on 2×AA
 * 
 * KEY ARCHITECTURAL DECISIONS:
 * 1. Split temperature reading into START -> WAIT -> READ phases
 * 2. Use timer callbacks to eliminate blocking delays
 * 3. System sleeps during 750ms conversion period
 * 4. Minimal delayMicroseconds() only for OneWire timing (unavoidable)
 */

#include "config.h"

// MAX31820 OneWire Commands
#define ONEWIRE_SKIP_ROM        0xCC
#define ONEWIRE_CONVERT_T       0x44
#define ONEWIRE_READ_SCRATCHPAD 0xBE
#define ONEWIRE_READ_ROM        0x33

// Temperature reading state machine states
typedef enum {
    TEMP_STATE_IDLE,
    TEMP_STATE_CONVERTING,
    TEMP_STATE_READY,
    TEMP_STATE_ERROR
} temp_reading_state_t;

// Global variables (minimize for power efficiency)
static uint8_t g_join_retry_count = 0;
static uint32_t g_last_tx_time = 0;
static temp_reading_state_t g_temp_state = TEMP_STATE_IDLE;
static int16_t g_last_temperature = INT16_MIN;
static uint32_t g_conversion_start_time = 0;

// Forward declarations
static void sensor_check_callback(void *);
static void send_sensor_data(void);
static void flash_led_single(void);
static void flash_led_continue_callback(void *);

/**
 * @brief Configure a GPIO pin with proper pull configuration
 */
static void configure_gpio(uint32_t pin, uint32_t mode) {
    GPIO_InitTypeDef GPIO_InitStruct = {0};
    
    // Enable GPIO clock (already enabled by RUI3)
    GPIO_InitStruct.Pin = (1 << (pin & 0x0F));
    
    if (mode == PIN_MODE_INPUT_PULLUP) {
        GPIO_InitStruct.Mode = GPIO_MODE_INPUT;
        GPIO_InitStruct.Pull = GPIO_PULLUP;
    } else if (mode == PIN_MODE_OUTPUT) {
        GPIO_InitStruct.Mode = GPIO_MODE_OUTPUT_PP;
        GPIO_InitStruct.Speed = GPIO_SPEED_FREQ_LOW;
        GPIO_InitStruct.Pull = GPIO_NOPULL;
    } else { // Analog mode for unused pins
        GPIO_InitStruct.Mode = GPIO_MODE_ANALOG;
        GPIO_InitStruct.Pull = GPIO_NOPULL;
    }
    
    // Determine port based on pin number
    GPIO_TypeDef* port = (pin < 16) ? GPIOA : GPIOB;
    HAL_GPIO_Init(port, &GPIO_InitStruct);
}

/**
 * @brief Configure all pins for minimum power consumption
 */
static void configure_all_pins_for_low_power(void) {
    // Configure all unused pins as INPUT_PULLUP to prevent floating
    for (uint32_t pin = 0; pin < 48; pin++) {
        if (pin != TEMP_SENSOR_PIN && pin != LED_STATUS_PIN) {
            pinMode(pin, INPUT_PULLUP);
        }
    }
    
    // Configure sensor pin with external pullup
    pinMode(TEMP_SENSOR_PIN, INPUT_PULLUP);
    
    // Configure LED pin as output (HIGH = LED off for active-low)
    pinMode(LED_STATUS_PIN, OUTPUT);
    digitalWrite(LED_STATUS_PIN, HIGH);
}

/**
 * @brief OneWire reset pulse for MAX31820
 * @return 1 if sensor present, 0 if not detected
 */
static uint8_t onewire_reset(void) {
    uint8_t presence;
    
    // Drive line low for 480µs
    pinMode(TEMP_SENSOR_PIN, OUTPUT);
    digitalWrite(TEMP_SENSOR_PIN, LOW);
    delayMicroseconds(480);
    
    // Release line and wait for presence pulse
    pinMode(TEMP_SENSOR_PIN, INPUT_PULLUP);
    delayMicroseconds(70);
    
    // Read presence (0 = device present)
    presence = !digitalRead(TEMP_SENSOR_PIN);
    delayMicroseconds(410);
    
    return presence;
}

/**
 * @brief Write byte to OneWire bus
 */
static void onewire_write_byte(uint8_t data) {
    for (uint8_t i = 0; i < 8; i++) {
        if (data & 0x01) {
            // Write 1: low for 1-15µs
            pinMode(TEMP_SENSOR_PIN, OUTPUT);
            digitalWrite(TEMP_SENSOR_PIN, LOW);
            delayMicroseconds(10);
            pinMode(TEMP_SENSOR_PIN, INPUT_PULLUP);
            delayMicroseconds(55);
        } else {
            // Write 0: low for 60-120µs
            pinMode(TEMP_SENSOR_PIN, OUTPUT);
            digitalWrite(TEMP_SENSOR_PIN, LOW);
            delayMicroseconds(65);
            pinMode(TEMP_SENSOR_PIN, INPUT_PULLUP);
            delayMicroseconds(5);
        }
        data >>= 1;
    }
}

/**
 * @brief Read byte from OneWire bus
 */
static uint8_t onewire_read_byte(void) {
    uint8_t data = 0;
    
    for (uint8_t i = 0; i < 8; i++) {
        // Initiate read slot
        pinMode(TEMP_SENSOR_PIN, OUTPUT);
        digitalWrite(TEMP_SENSOR_PIN, LOW);
        delayMicroseconds(3);
        
        // Release and read
        pinMode(TEMP_SENSOR_PIN, INPUT_PULLUP);
        delayMicroseconds(10);
        
        // Sample the bit
        if (digitalRead(TEMP_SENSOR_PIN)) {
            data |= (1 << i);
        }
        
        delayMicroseconds(53);
    }
    
    return data;
}

/**
 * @brief Start temperature conversion (non-blocking)
 * This initiates the conversion and sets up for later reading
 */
static bool start_temperature_conversion(void) {
    // Check sensor presence
    if (!onewire_reset()) {
        g_temp_state = TEMP_STATE_ERROR;
        return false;
    }
    
    // Start temperature conversion
    onewire_write_byte(ONEWIRE_SKIP_ROM);
    onewire_write_byte(ONEWIRE_CONVERT_T);
    
    // Mark conversion start time and state
    g_conversion_start_time = millis();
    g_temp_state = TEMP_STATE_CONVERTING;
    
    return true;
}

/**
 * @brief Check if temperature conversion is complete and read if ready
 * This is called periodically to check conversion status
 * @return true if data is ready (success or error), false if still converting
 */
static bool check_temperature_ready(void) {
    if (g_temp_state != TEMP_STATE_CONVERTING) {
        return (g_temp_state == TEMP_STATE_READY || g_temp_state == TEMP_STATE_ERROR);
    }
    
    // Check if 750ms has elapsed (MAX31820 12-bit conversion time)
    if ((millis() - g_conversion_start_time) < 750) {
        return false; // Still converting
    }
    
    // Conversion time elapsed, read the result
    uint8_t scratchpad[9];
    
    // Read temperature result
    if (!onewire_reset()) {
        g_temp_state = TEMP_STATE_ERROR;
        g_last_temperature = INT16_MIN;
        return true;
    }
    
    onewire_write_byte(ONEWIRE_SKIP_ROM);
    onewire_write_byte(ONEWIRE_READ_SCRATCHPAD);
    
    // Read scratchpad (total ~2ms of delayMicroseconds)
    for (uint8_t i = 0; i < 9; i++) {
        scratchpad[i] = onewire_read_byte();
    }
    
    // Basic validation - check if we got valid data
    if (scratchpad[8] != 0 || scratchpad[0] != 0xFF || scratchpad[1] != 0xFF) {
        // Convert raw temperature
        int16_t raw_temp = (scratchpad[1] << 8) | scratchpad[0];
        
        // Sanity check temperature range (-55°C to +125°C)
        if (raw_temp >= -880 && raw_temp <= 2000) {
            g_last_temperature = (raw_temp * 100) / 16; // Convert to hundredths
            g_temp_state = TEMP_STATE_READY;
        } else {
            g_last_temperature = INT16_MIN; // Out of range
            g_temp_state = TEMP_STATE_ERROR;
        }
    } else {
        g_last_temperature = INT16_MIN; // Invalid data
        g_temp_state = TEMP_STATE_ERROR;
    }
    
    return true; // Data is ready (either valid or error)
}

/**
 * @brief Get the last temperature reading
 * @return Temperature in hundredths of degree C, or INT16_MIN on error
 */
static int16_t get_last_temperature(void) {
    return g_last_temperature;
}

/**
 * @brief Reset temperature state machine for new reading
 */
static void reset_temperature_state(void) {
    g_temp_state = TEMP_STATE_IDLE;
    g_last_temperature = INT16_MIN;
}

// LED control state variables
static uint8_t g_led_flashes_remaining = 0;

/**
 * @brief Flash LED briefly (ultra-low power version) - TIMER BASED
 * Uses minimal on-time and timer callbacks instead of blocking delays
 */
static void flash_led_quick(uint8_t count) {
    if (count == 0 || g_led_flashes_remaining > 0) return; // Prevent overlapping
    
    g_led_flashes_remaining = count;
    flash_led_single();
}

/**
 * @brief Single LED flash with timer-based delay for multiple flashes
 */
static void flash_led_single(void) {
    // Flash LED for 1ms (delayMicroseconds OK for OneWire timing per RUI3 docs)
    digitalWrite(LED_STATUS_PIN, LOW);   // LED on (active-low)
    delayMicroseconds(1000);             // 1ms flash - acceptable microsecond timing
    digitalWrite(LED_STATUS_PIN, HIGH);  // LED off
    
    g_led_flashes_remaining--;
    
    if (g_led_flashes_remaining > 0) {
        // Use timer for 200ms delay instead of blocking delayMicroseconds
        api.system.timer.create(RAK_TIMER_2, flash_led_continue_callback, RAK_TIMER_ONE_SHOT);
        api.system.timer.start(RAK_TIMER_2, 200, NULL);
    }
}

/**
 * @brief Timer callback to continue LED flashing sequence
 */
static void flash_led_continue_callback(void *) {
    flash_led_single();
}

/**
 * @brief Load OTAA credentials from RUI3 internal storage
 * Compatible with provision_credentials.py AT command provisioning
 */
static bool load_otaa_credentials(void) {
    uint8_t appeui[8];
    uint8_t appkey[16];
    
    // Try to read AppEUI from RUI3 internal storage (set by AT+APPEUI command)
    if (!api.lorawan.appeui.get(appeui, 8)) {
        return false;
    }
    
    // Try to read AppKey from RUI3 internal storage (set by AT+APPKEY command)  
    if (!api.lorawan.appkey.get(appkey, 16)) {
        return false;
    }
    
    // Check if credentials are valid (not all zeros)
    bool appeui_valid = false;
    bool appkey_valid = false;
    
    for (int i = 0; i < 8; i++) {
        if (appeui[i] != 0x00) {
            appeui_valid = true;
            break;
        }
    }
    
    for (int i = 0; i < 16; i++) {
        if (appkey[i] != 0x00) {
            appkey_valid = true;
            break;
        }
    }
    
    // Return true only if both credentials are non-zero (provisioned)
    return (appeui_valid && appkey_valid);
}

/**
 * @brief Timer callback to start temperature conversion
 * This is called every 5 minutes to begin the measurement process
 */
static void sensor_start_callback(void *) {
    // Reset temperature state and start new conversion
    reset_temperature_state();
    
    if (start_temperature_conversion()) {
        // Conversion started successfully
        // Create a one-shot timer to check for completion after 800ms
        api.system.timer.create(RAK_TIMER_1, sensor_check_callback, RAK_TIMER_ONE_SHOT);
        api.system.timer.start(RAK_TIMER_1, 800, NULL); // 800ms = 750ms + margin
    } else {
        // Sensor error - send error payload immediately
        send_sensor_data();
    }
}

/**
 * @brief Timer callback to check temperature conversion completion
 * Called 800ms after conversion start to read the result
 */
static void sensor_check_callback(void *) {
    if (check_temperature_ready()) {
        // Temperature data is ready (success or error)
        send_sensor_data();
    } else {
        // Still converting (shouldn't happen) - try again in 100ms
        api.system.timer.create(RAK_TIMER_1, sensor_check_callback, RAK_TIMER_ONE_SHOT);
        api.system.timer.start(RAK_TIMER_1, 100, NULL);
    }
}

/**
 * @brief Send sensor data via LoRaWAN
 * This function sends the temperature and battery data
 */
static void send_sensor_data(void) {
    uint8_t payload[4];
    int16_t temp_hundredths = get_last_temperature();
    
    if (temp_hundredths == INT16_MIN) {
        // Sensor error
        payload[0] = 0xFF;
        payload[1] = 0xFF;
        payload[2] = 0xFF;
        payload[3] = 0xFF;
    } else {
        // Pack temperature (MSB first)
        payload[0] = (temp_hundredths >> 8) & 0xFF;
        payload[1] = temp_hundredths & 0xFF;
        
        // Add battery voltage (in mV)
        float battery_v = api.system.bat.get();
        uint16_t battery_mv = (uint16_t)(battery_v * 1000);
        payload[2] = (battery_mv >> 8) & 0xFF;
        payload[3] = battery_mv & 0xFF;
    }
    
    // Check if joined
    if (!api.lorawan.njs.get()) {
        // Not joined, attempt rejoin
        if (g_join_retry_count < MAX_JOIN_RETRIES) {
            api.lorawan.join();
            g_join_retry_count++;
        }
        return;
    }
    
    // Send data (unconfirmed for power saving)
    flash_led_quick(1); // Brief flash before TX
    api.lorawan.send(sizeof(payload), payload, LORAWAN_PORT, false, 0);
    
    g_last_tx_time = millis();
}

/**
 * @brief LoRaWAN join callback
 */
static void join_callback(int32_t status) {
    if (status == 0) {
        // Join successful
        g_join_retry_count = 0;
        flash_led_quick(3); // Triple flash on join
    }
}

/**
 * @brief LoRaWAN receive callback
 */
static void recv_callback(SERVICE_LORA_RECEIVE_T *data) {
    if (data->Port == 99 && data->BufferSize > 0) {
        // Configuration port - could implement remote config here
        // For now, just flash LED to acknowledge
        flash_led_quick(2);
    }
}

/**
 * @brief Main setup function
 */
void setup() {
    // CRITICAL: Enable low power mode immediately
    api.system.lpm.set(1);
    
    // Configure all pins for minimum power consumption
    configure_all_pins_for_low_power();
    
    // Brief startup indication
    flash_led_quick(2);
    
    // Configure LoRaWAN parameters
    if (!api.lorawan.nwm.set(1)) {
        // Network mode error - flash error pattern
        flash_led_quick(5);
        api.system.reboot();
    }
    
    // Set region
    if (!api.lorawan.band.set(OTAA_BAND)) {
        flash_led_quick(5);
        api.system.reboot();
    }
    
    // Set sub-band for US915
    uint16_t mask = CHANNEL_MASK;
    if (!api.lorawan.mask.set(&mask)) {
        flash_led_quick(5);
        api.system.reboot();
    }
    
    // Configure Class A (lowest power)
    api.lorawan.deviceClass.set(RAK_LORA_CLASS_A);
    
    // Set network join mode to OTAA
    api.lorawan.njm.set(RAK_LORA_OTAA);
    
    // CRITICAL: Read DevEUI from chip (never hardcode!)
    uint8_t devEui[8];
    api.lorawan.deui.get(devEui, 8);
    api.lorawan.deui.set(devEui, 8);
    
    // Check if device has been provisioned with valid credentials
    if (!load_otaa_credentials()) {
        // No valid credentials found - device needs provisioning
        // Use provision_credentials.py tool to program AppEUI and AppKey
        // Flash error pattern to indicate provisioning needed
        while (true) {
            flash_led_quick(10); // 10 flashes = provisioning required
            api.system.sleep.all(30000); // Sleep 30 seconds between error patterns
        }
        // Device will stay in this loop until provisioned and rebooted
    }
    
    // Configure optimal power settings
    api.lorawan.adr.set(true);      // Enable ADR for automatic optimization
    api.lorawan.cfm.set(false);     // Unconfirmed messages (save power)
    api.lorawan.dr.set(DR_SETTING); // Initial data rate
    api.lorawan.txp.set(TX_POWER);  // Conservative TX power
    api.lorawan.rety.set(0);        // No retries (save power)
    
    // Register callbacks
    api.lorawan.registerJoinCallback(join_callback);
    api.lorawan.registerRecvCallback(recv_callback);
    
    // Attempt initial join
    api.lorawan.join();
    
    // Create and start periodic timer for sensor readings
    api.system.timer.create(RAK_TIMER_0, sensor_start_callback, RAK_TIMER_PERIODIC);
    api.system.timer.start(RAK_TIMER_0, TX_INTERVAL_MS, NULL);
    
    // Enter sleep immediately after setup
    api.system.sleep.all();
}

/**
 * @brief Main loop - MUST be destroyed for ultra-low power
 */
void loop() {
    // CRITICAL: Destroy the loop to enable event-driven architecture
    // This is essential for achieving 2-3µA sleep current
    api.system.scheduler.task.destroy();
    
    // This code will never execute after task destruction
}
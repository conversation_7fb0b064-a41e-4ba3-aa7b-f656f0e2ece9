{"name": "coldra-factory-tool", "version": "1.0.0", "description": "COLDRA Factory Production Tool - Web-based manufacturing system for RAK3172-based LoRaWAN temperature sensors", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "stop": "taskkill /f /im node.exe 2>nul || echo No Node.js processes found", "restart": "npm run stop && npm start", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": ["coldra", "lo<PERSON>an", "factory", "manufacturing", "rak3172", "production"], "author": "UST Development Team", "license": "PROPRIETARY", "dependencies": {"cors": "^2.8.5", "express": "^4.18.2", "helmet": "^7.1.0", "morgan": "^1.10.0", "sqlite3": "^5.1.7", "winston": "^3.17.0", "winston-daily-rotate-file": "^4.7.1"}, "devDependencies": {"nodemon": "^3.0.2"}, "engines": {"node": ">=16.0.0", "npm": ">=8.0.0"}}
/**
 * Logger - COLDRA Factory Tool
 * Structured logging with file rotation
 */

const winston = require('winston');
const DailyRotateFile = require('winston-daily-rotate-file');
const path = require('path');

// Create logs directory if it doesn't exist
const fs = require('fs');
const logsDir = path.join(__dirname, '../../logs');
if (!fs.existsSync(logsDir)) {
    fs.mkdirSync(logsDir, { recursive: true });
}

// Custom format for COLDRA Factory Tool
const customFormat = winston.format.combine(
    winston.format.timestamp({ format: 'YYYY-MM-DD HH:mm:ss' }),
    winston.format.errors({ stack: true }),
    winston.format.printf(({ timestamp, level, message, stack, ...meta }) => {
        let log = `${timestamp} [${level.toUpperCase()}] ${message}`;
        
        // Add metadata if present
        if (Object.keys(meta).length > 0) {
            log += ` | ${JSON.stringify(meta)}`;
        }
        
        // Add stack trace for errors
        if (stack) {
            log += `\n${stack}`;
        }
        
        return log;
    })
);

// File transport with daily rotation
const fileTransport = new DailyRotateFile({
    filename: path.join(logsDir, 'coldra-factory-%DATE%.log'),
    datePattern: 'YYYY-MM-DD',
    maxSize: '20m',
    maxFiles: '30d',
    format: customFormat
});

// Error file transport
const errorTransport = new DailyRotateFile({
    filename: path.join(logsDir, 'coldra-factory-error-%DATE%.log'),
    datePattern: 'YYYY-MM-DD',
    level: 'error',
    maxSize: '20m',
    maxFiles: '30d',
    format: customFormat
});

// Console transport for development
const consoleTransport = new winston.transports.Console({
    format: winston.format.combine(
        winston.format.colorize(),
        winston.format.timestamp({ format: 'HH:mm:ss' }),
        winston.format.printf(({ timestamp, level, message }) => {
            return `${timestamp} [${level}] ${message}`;
        })
    )
});

// Create logger instance
const logger = winston.createLogger({
    level: process.env.LOG_LEVEL || 'info',
    transports: [
        fileTransport,
        errorTransport,
        consoleTransport
    ]
});

// Add production-specific methods
logger.production = {
    deviceStart: (serialNumber, operator) => {
        logger.info('Device production started', { 
            serial_number: serialNumber, 
            operator: operator,
            event: 'DEVICE_START'
        });
    },
    
    deviceComplete: (serialNumber, status, duration) => {
        logger.info('Device production completed', { 
            serial_number: serialNumber, 
            status: status,
            duration_seconds: duration,
            event: 'DEVICE_COMPLETE'
        });
    },
    
    hardwareEvent: (event, details) => {
        logger.info('Hardware event', { 
            event: event, 
            details: details,
            event_type: 'HARDWARE'
        });
    },
    
    error: (operation, error, context = {}) => {
        logger.error('Production error', { 
            operation: operation, 
            error: error.message,
            context: context,
            event: 'ERROR'
        });
    }
};

module.exports = logger;

# COLDRA Factory Tool - Requirements Document

## Introduction

The COLDRA Factory Production Tool requires detailed implementation logic to automate the manufacturing process of RAK3172-based LoRaWAN temperature sensors. This specification defines the concrete algorithms, state machines, and interconnectivity logic needed for a production-ready system that can handle 480-720 devices per 8-hour shift with >95% yield.

## Requirements

### Requirement 1: Serial Number Generation with Atomic Assignment

**User Story:** As a production operator, I want the system to generate unique serial numbers automatically, so that each device has a guaranteed unique identifier with simple and reliable logic.

#### Acceptance Criteria

1. WHEN the system starts a device workflow THEN it SHALL generate the next serial number using format DDMMYY-R-NNN by querying the current maximum counter for the date/region
2. WHEN a device completes successfully THEN it SHALL commit the serial number to the database using atomic transaction with auto-increment
3. WHEN a device fails or is cancelled THEN it SHALL not commit any serial number, leaving no gaps in the sequence
4. WHEN the daily counter reaches 999 THEN the system SHALL prevent further production for that region/date combination and alert the operator
5. WHEN the system starts up THEN it SHALL initialize by reading the highest existing counter for each region/date combination
6. WHEN generating serial numbers THEN the system SHALL complete the operation within 100ms under normal database conditions

### Requirement 2: RAK3172 RUI3 Firmware Interface with AT Command Protocol

**User Story:** As a production operator, I want reliable communication with RAK3172 devices running RUI3 firmware, so that device programming and provisioning works with the actual firmware interface.

#### Acceptance Criteria

1. WHEN connecting to ST-Link THEN the system SHALL program the COLDRA_OTAA_UltraLowPower.ino firmware (version 2.2.0) to RAK3172 devices using STM32CubeProgrammer CLI
2. WHEN communicating via FT232 UART THEN the system SHALL use 115200 baud rate on the detected COM port and implement RUI3 AT command protocol with 1-second timeout per command
3. WHEN reading DevEUI THEN the system SHALL send AT+DEVEUI=? command and parse the 16-character hex response from the RAK3172 chip
4. WHEN provisioning AppEUI THEN the system SHALL send AT+APPEUI=<16-hex-chars> command and verify "OK" response within 1 second
5. WHEN provisioning AppKey THEN the system SHALL send AT+APPKEY=<32-hex-chars> command and verify "OK" response within 1 second
6. WHEN saving credentials THEN the system SHALL send AT&W command and verify "OK" response to save configuration to RUI3 internal storage
7. WHEN testing device functionality THEN the system SHALL send AT+JOIN command and monitor AT command responses for join status confirmation
8. WHEN join test fails THEN the system SHALL retry join command up to 3 times with 30-second timeout per attempt

### Requirement 3: COLDRA Firmware Programming with Version Validation

**User Story:** As a production operator, I want the system to program the correct COLDRA firmware version with validation, so that all devices have consistent ultra-low power functionality.

#### Acceptance Criteria

1. WHEN programming firmware THEN the system SHALL flash COLDRA_OTAA_UltraLowPower.hex compiled binary for the target region (US915, EU868, or IN865) using STM32CubeProgrammer CLI
2. WHEN selecting firmware THEN the system SHALL validate the firmware file exists, has correct file size (>50KB, <256KB), and matches the selected region configuration
3. WHEN programming completes THEN the system SHALL verify programming success by reading back flash memory and comparing checksums
4. WHEN validating firmware THEN the system SHALL establish UART communication and send AT command to verify device responds within 5 seconds
5. WHEN firmware programming fails THEN the system SHALL retry up to 3 times and log detailed error information including STM32CubeProgrammer exit codes and error messages
6. WHEN device is programmed THEN the system SHALL verify UART communication is established and AT commands respond correctly

### Requirement 4: Database Transaction Management with Concurrency Control

**User Story:** As a production manager, I want all device data to be stored reliably with full transaction integrity, so that no production data is ever lost or corrupted.

#### Acceptance Criteria

1. WHEN recording device data THEN the system SHALL use database transactions with ACID properties
2. WHEN multiple operators work simultaneously THEN the system SHALL handle concurrent access using row-level locking
3. IF a database operation fails THEN the system SHALL rollback the entire transaction and retry up to 3 times
4. WHEN the database is unavailable THEN the system SHALL cache operations locally and sync when connection is restored
5. WHEN updating device status THEN the system SHALL use optimistic locking to prevent lost updates
6. WHEN exporting data THEN the system SHALL maintain read consistency using snapshot isolation

### Requirement 5: Production Workflow State Machine with Error Recovery

**User Story:** As a production operator, I want the system to guide me through each production step with automatic error recovery, so that I can maintain high throughput even when issues occur.

#### Acceptance Criteria

1. WHEN starting production THEN the system SHALL initialize a state machine with states: IDLE, PROGRAMMING, PROVISIONING, TESTING, LABELING, COMPLETE, ERROR
2. WHEN an error occurs THEN the system SHALL transition to ERROR state and execute appropriate recovery procedures
3. WHEN in ERROR state THEN the system SHALL provide clear instructions and allow manual intervention or automatic retry
4. WHEN resuming from error THEN the system SHALL continue from the last successful checkpoint
5. WHEN completing a device THEN the system SHALL automatically transition to IDLE state for the next device
6. WHEN the operator pauses production THEN the system SHALL save current state and allow resumption

### Requirement 6: TTN Credential Management with JSON Configuration

**User Story:** As a security administrator, I want TTN credentials managed through validated JSON configuration files, so that credential integrity is guaranteed and deployment is secure.

#### Acceptance Criteria

1. WHEN loading TTN credentials THEN the system SHALL read from a JSON file containing JoinEUI (application-wide) and AppKey (application-wide) with JSON schema validation
2. WHEN validating credential files THEN the system SHALL verify JSON format, field types, and value ranges before accepting any credentials
3. WHEN provisioning DevEUI THEN the system SHALL read the value from the RAK3172 module barcode (AC1F09FFE1234567 format) and never write to it
4. WHEN provisioning JoinEUI THEN the system SHALL use the same 8-byte value (FEDCBA0987654321 format) for all devices in the application as configured in TTN console
5. WHEN provisioning AppKey THEN the system SHALL use the same 32-byte AES-128 key for all devices in the application, encrypted with Windows DPAPI for storage
6. WHEN storing credentials in database THEN the system SHALL encrypt AppKey and never log it in plaintext, while DevEUI and JoinEUI can be stored as plaintext identifiers
7. WHEN validating provisioned credentials THEN the system SHALL perform LoRaWAN join test to verify the DevEUI/JoinEUI/AppKey combination works with TTN network

### Requirement 7: COLDRA Firmware Quality Testing with AT Command Validation

**User Story:** As a quality engineer, I want comprehensive testing that validates the COLDRA firmware functionality using AT commands, so that devices meet specifications and LoRaWAN connectivity requirements.

#### Acceptance Criteria

1. WHEN testing LoRaWAN join THEN the system SHALL send AT+JOIN command and monitor AT command responses for join confirmation within 90 seconds if gateway is available
2. WHEN gateway is unavailable THEN the system SHALL mark join test as "NOT_TESTED" and continue with credential verification and metadata recording
3. WHEN testing firmware version THEN the system SHALL send AT+VER=? command and verify response contains "2.2.0" firmware version string
4. WHEN testing device configuration THEN the system SHALL send AT+BAND=? and AT+MASK=? commands to verify correct regional settings are programmed
5. WHEN testing credential storage THEN the system SHALL send AT+APPEUI=? and verify the provisioned AppEUI is correctly stored and readable
6. WHEN testing temperature sensor THEN the system SHALL send AT command to read temperature and verify response is reasonable room temperature (15-35°C range)
7. WHEN testing basic functionality THEN the system SHALL send AT+BAT=? command to read battery voltage and verify response is between 2000-4000 (mV)
8. WHEN all tests complete THEN the system SHALL record test completion time, firmware version, device configuration, join test status, and all credential metadata in the database

### Requirement 8: Power Consumption Measurement with PPK2 Integration

**User Story:** As a quality engineer, I want to record power consumption measurements using Nordic PPK2, so that each device has factory power consumption data for quality tracking.

#### Acceptance Criteria

1. WHEN PPK2 is connected THEN the system SHALL detect Nordic PPK2 device by USB VID:PID (1915:521F) and display PPK2 connection status
2. WHEN measuring power consumption THEN the system SHALL provide manual entry fields for "Transmission Current (µA)" and "Sleep Current (µA)" measurements
3. WHEN operator enters current measurements THEN the system SHALL validate values are within expected ranges (Sleep: 1-10µA, Transmission: 10-50mA)
4. WHEN power measurements are recorded THEN the system SHALL store the values in the database with timestamp and operator ID for factory traceability
5. WHEN PPK2 is not available THEN the system SHALL allow production to continue but mark power measurements as "NOT_MEASURED"
6. WHEN displaying device history THEN the system SHALL show recorded power consumption values alongside other factory test data
7. WHEN generating reports THEN the system SHALL include power consumption statistics and trends for quality analysis

### Requirement 9: Label Generation with QR Code and Printing Integration

**User Story:** As a production operator, I want automatic label generation and printing, so that devices are properly identified and trackable throughout their lifecycle.

#### Acceptance Criteria

1. WHEN generating labels THEN the system SHALL create QR codes containing device serial number, DevEUI, firmware version, and manufacturing date
2. WHEN printing labels THEN the system SHALL support multiple printer types (Zebra ZPL, Brother P-touch, PDF export)
3. WHEN QR code generation fails THEN the system SHALL retry with different error correction levels
4. IF printer is unavailable THEN the system SHALL queue print jobs and process when printer becomes available
5. WHEN label is printed THEN the system SHALL verify print completion and update device status
6. WHEN exporting to PDF THEN the system SHALL generate high-resolution labels suitable for manual printing

### Requirement 10: Production Throughput and Performance Optimization

**User Story:** As a production manager, I want the system to achieve realistic throughput targets with clear performance metrics, so that I can plan production capacity accurately.

#### Acceptance Criteria

1. WHEN calculating throughput targets THEN the system SHALL target 60-90 seconds per device cycle time (programming + provisioning + testing + labeling)
2. WHEN operating at target efficiency THEN the system SHALL achieve 480-720 devices per 8-hour shift (60-90 devices per hour)
3. WHEN multiple operators use the system THEN each operator SHALL work independently with separate database transactions to prevent blocking
4. WHEN database contention occurs THEN the system SHALL implement retry logic with exponential backoff (100ms, 200ms, 400ms) for serial number generation
5. WHEN performance monitoring is active THEN the system SHALL track and display average cycle time, current throughput rate, and daily production count
6. WHEN cycle time exceeds 120 seconds THEN the system SHALL alert the operator and log performance degradation for analysis
7. WHEN daily production targets are set THEN the system SHALL calculate required throughput rate and display progress toward daily goals

### Requirement 11: Real-time Monitoring and Statistics with Performance Tracking

**User Story:** As a production manager, I want real-time visibility into production metrics and performance, so that I can optimize throughput and identify issues quickly.

#### Acceptance Criteria

1. WHEN devices are processed THEN the system SHALL update real-time counters for daily production by region
2. WHEN calculating yield THEN the system SHALL compute pass/fail ratios with rolling averages over configurable time periods
3. WHEN performance degrades THEN the system SHALL alert operators when cycle time exceeds thresholds
4. WHEN displaying statistics THEN the system SHALL refresh metrics every 5 seconds without impacting production performance
5. WHEN generating reports THEN the system SHALL export detailed production data in CSV format with customizable date ranges
6. WHEN tracking operator performance THEN the system SHALL record individual operator statistics while maintaining privacy

### Requirement 12: Configuration Management with SWE Compliance and Version Control

**User Story:** As a system administrator, I want simple configuration management that follows SWE standards, so that all changes are traceable and recoverable.

#### Acceptance Criteria

1. WHEN updating any configuration THEN the system SHALL increment application version following semantic versioning (MAJOR.MINOR.PATCH) as per SWE guidelines
2. WHEN configuration changes THEN the system SHALL log changes with timestamp, operator ID, and description in the audit database
3. WHEN loading TTN credential JSON files THEN the system SHALL validate JSON schema and field formats before accepting
4. WHEN firmware paths are updated THEN the system SHALL validate file existence, file size (>50KB, <512KB), and .hex/.bin extension before accepting
5. WHEN regional settings change THEN the system SHALL validate region codes match supported values (US915, EU868, IN865)
6. WHEN configuration is modified THEN the system SHALL create a timestamped backup copy in the backups folder
7. WHEN validating configuration THEN the system SHALL check file paths exist, JSON files are valid, and required fields are present

### Requirement 13: TTN Credential Utility for Offline JSON Generation

**User Story:** As a production setup administrator, I want a separate utility to generate validated TTN credential JSON files offline, so that credential files are prepared securely before production begins.

#### Acceptance Criteria

1. WHEN creating credential files THEN the utility SHALL generate JSON format with fields: application_name, join_eui, app_key, region, and created_date
2. WHEN validating input THEN the utility SHALL verify JoinEUI is 16 hex characters (8 bytes), AppKey is 32 hex characters (16 bytes), and region matches supported values (US915, EU868, IN865)
3. WHEN generating output THEN the utility SHALL create human-readable JSON with proper formatting and include metadata for traceability
4. WHEN the utility completes THEN it SHALL display the file path and credential summary for verification before use in production
5. WHEN importing to main application THEN the factory tool SHALL validate JSON schema and credential format before accepting the file

### Requirement 14: Device Read-Back and Field Return Analysis

**User Story:** As a quality engineer, I want to analyze devices returned from the field by entering their serial number, so that I can retrieve complete manufacturing history and diagnose issues.

#### Acceptance Criteria

1. WHEN entering a serial number THEN the system SHALL provide a dedicated "Device Lookup" interface for manual serial number entry
2. WHEN a valid serial number is entered THEN the system SHALL retrieve and display complete manufacturing history including programming date, test results, operator ID, and firmware version
3. WHEN connecting a returned device via UART THEN the system SHALL read current DevEUI, firmware version, and operational counters using AT commands
4. WHEN analyzing returned device THEN the system SHALL compare current device state with original manufacturing data and highlight any discrepancies
5. WHEN device analysis is complete THEN the system SHALL generate a diagnostic report with recommendations for repair or replacement
6. WHEN invalid serial number is entered THEN the system SHALL display clear error message and suggest format validation
7. WHEN device lookup is performed THEN the system SHALL log the lookup activity with operator ID and timestamp for audit purposes

### Requirement 15: USB Hardware Detection and Connection Management

**User Story:** As a production operator, I want the system to automatically detect ST-Link and FT232 USB connections, so that I know when the hardware is ready for production.

#### Acceptance Criteria

1. WHEN the system starts THEN it SHALL enumerate USB devices and identify ST-Link programmer by VID:PID (0483:374B or 0483:3748) and FT232 by VID:PID (0403:6001)
2. WHEN ST-Link is detected THEN the system SHALL validate connection by executing STM32CubeProgrammer CLI with "-c port=SWD" and verify successful connection response
3. WHEN FT232 is detected THEN the system SHALL identify the assigned COM port number and test UART communication at 115200 baud
4. WHEN hardware is disconnected THEN the system SHALL detect USB device removal within 3 seconds using Windows USB notification events
5. WHEN hardware is reconnected THEN the system SHALL automatically re-enumerate devices and re-establish connections without requiring application restart
6. WHEN RAK3172 device is connected THEN the system SHALL detect device presence by attempting ST-Link connection and receiving successful chip ID response
7. WHEN device is removed from fixture THEN the system SHALL detect ST-Link connection failure and automatically reset workflow state to IDLE
8. WHEN both ST-Link and FT232 are connected and validated THEN the system SHALL display "Hardware Ready" status with green indicators

### Requirement 16: Hardware Interface State Machine and Connection Logic

**User Story:** As a production operator, I want the system to manage hardware connections through a clear state machine, so that I understand the system status and can troubleshoot connection issues.

#### Acceptance Criteria

1. WHEN system initializes THEN it SHALL implement hardware state machine with states: DISCONNECTED, ST_LINK_DETECTED, FT232_DETECTED, BOTH_CONNECTED, DEVICE_DETECTED, READY_FOR_PRODUCTION
2. WHEN in DISCONNECTED state THEN the system SHALL continuously scan for USB devices every 2 seconds and transition to appropriate detection state
3. WHEN ST-Link is detected THEN the system SHALL execute "STM32_Programmer_CLI.exe -c port=SWD -l" to list connected devices and validate ST-Link functionality
4. WHEN FT232 is detected THEN the system SHALL open the COM port at 115200 baud, send "AT\r\n" command, and verify response within 1 second
5. WHEN both devices are connected THEN the system SHALL attempt device detection by executing "STM32_Programmer_CLI.exe -c port=SWD -r32 0x1FFF7590 8" to read chip ID
6. WHEN chip ID is successfully read THEN the system SHALL transition to DEVICE_DETECTED state and enable production workflow
7. WHEN any hardware connection fails THEN the system SHALL transition to appropriate error state and display specific troubleshooting instructions
8. WHEN device is removed THEN the system SHALL detect ST-Link connection failure and transition back to BOTH_CONNECTED state

### Requirement 16: Single Fixture Operation and Device Management

**User Story:** As a production operator, I want the system to work with a single test fixture per workstation, so that device handling is simple and reliable.

#### Acceptance Criteria

1. WHEN system is deployed THEN it SHALL operate with one test fixture per workstation containing ST-Link, FT232, and PPK2 connections
2. WHEN a device is inserted THEN the operator SHALL manually place the RAK3172 device into the fixture and the system SHALL detect presence via ST-Link connection
3. WHEN device detection occurs THEN the system SHALL identify the specific device by reading DevEUI and display it prominently in the UI
4. WHEN multiple devices are processed THEN the operator SHALL complete one device fully before inserting the next device into the same fixture
5. WHEN device is removed THEN the system SHALL detect disconnection and automatically reset to IDLE state for the next device
6. WHEN fixture hardware fails THEN the system SHALL provide clear troubleshooting guidance specific to ST-Link, FT232, or PPK2 connection issues
7. WHEN workstation setup is validated THEN the system SHALL verify all required hardware is connected and display "Fixture Ready" status

### Requirement 17: Operator User Experience and Workflow Guidance

**User Story:** As a production operator, I want clear visual guidance and intuitive workflow steps with UST brand styling, so that I can work efficiently without confusion or errors.

#### Acceptance Criteria

1. WHEN starting a shift THEN the system SHALL display a clear dashboard with Dark Teal (#006E74) background showing hardware status, daily production count, and current workflow state in White (#FFFFFF) text
2. WHEN device is inserted THEN the system SHALL show step-by-step progress with Light Teal (#0097AC) progress indicators: "Insert Device" → "Programming" → "Provisioning" → "Testing" → "Remove Device"
3. WHEN an error occurs THEN the system SHALL display specific error messages in Soft Black (#231F20) text with Light Teal (#0097AC) accent borders and recommended actions in plain language
4. WHEN operator intervention is required THEN the system SHALL provide clear instructions with UST color-coded visual cues: Dark Teal for normal status, Light Teal for active states, and appropriate contrast for alerts
5. WHEN entering manual data THEN the system SHALL provide input validation with Light Teal (#0097AC) highlighting for active fields and immediate feedback using UST color scheme
6. WHEN workflow is paused THEN the system SHALL clearly indicate current state with Dark Teal (#006E74) background and provide Light Teal (#0097AC) "Resume" button to continue from the same step
7. WHEN training new operators THEN the system SHALL include help tooltips with Light Teal (#0097AC) backgrounds and a "Practice Mode" that simulates workflow without actual device programming

### Requirement 18: Security Implementation and Access Control

**User Story:** As a security administrator, I want comprehensive security controls for operator access and data protection, so that production data and credentials are protected.

#### Acceptance Criteria

1. WHEN operator starts the application THEN the system SHALL require Windows authentication and validate user permissions against local user groups
2. WHEN storing AppKeys THEN the system SHALL use Windows DPAPI with user-specific encryption keys and never store plaintext credentials
3. WHEN communicating with database THEN the system SHALL use encrypted SQLite database with password protection and connection string encryption
4. WHEN exporting data THEN the system SHALL log all export activities with operator ID, timestamp, and data scope for audit purposes
5. WHEN accessing sensitive functions THEN the system SHALL implement role-based access (Operator, Supervisor, Administrator) with appropriate UI restrictions
6. WHEN security events occur THEN the system SHALL log failed authentication attempts, unauthorized access attempts, and configuration changes
7. WHEN handling TTN credentials THEN the system SHALL mask AppKey values in all UI displays and log files while preserving functionality

### Requirement 19: Offline Operation and Data Synchronization

**User Story:** As a production operator, I want the system to continue working when network connectivity is lost, so that production is not interrupted by network issues.

#### Acceptance Criteria

1. WHEN database connection is lost THEN the system SHALL automatically switch to offline mode and cache all device records locally in SQLite
2. WHEN operating offline THEN the system SHALL continue firmware programming, credential provisioning, and testing with full functionality
3. WHEN offline mode is active THEN the system SHALL display clear "OFFLINE" indicator and queue all database operations for later synchronization
4. WHEN network connectivity is restored THEN the system SHALL automatically detect connection and prompt operator to synchronize cached data
5. WHEN synchronizing data THEN the system SHALL upload cached records in chronological order and handle any serial number conflicts by alerting the operator
6. WHEN conflict resolution is needed THEN the system SHALL display conflicting records and allow operator to choose resolution (keep local, keep remote, or merge)
7. WHEN offline capacity is reached THEN the system SHALL alert operator when local cache exceeds 1000 records and recommend immediate synchronization

### Requirement 20: LoRaWAN Network Server Compatibility

**User Story:** As a system administrator, I want the system to support TTN primarily but allow for future network server integration, so that the system is not locked to a single provider.

#### Acceptance Criteria

1. WHEN configuring network settings THEN the system SHALL default to TTN (The Things Network) configuration but allow custom network server settings
2. WHEN using TTN THEN the system SHALL use standard OTAA credentials (DevEUI, JoinEUI, AppKey) as currently implemented
3. WHEN future network servers are needed THEN the system SHALL support configuration of custom join server addresses and credential formats through JSON configuration
4. WHEN testing join functionality THEN the system SHALL use configurable timeout values and join success criteria that can be adapted for different network servers
5. WHEN network server is unavailable THEN the system SHALL gracefully handle join test failures and continue with credential programming and metadata recording
6. WHEN exporting device data THEN the system SHALL include network server configuration information for traceability and future migration support
7. WHEN validating credentials THEN the system SHALL use configurable validation rules that can accommodate different network server requirements

### Requirement 21: SWE Software Component Tracking and Version Management

**User Story:** As a system administrator, I want a simple interface to track software components and their versions, so that I can maintain SWE compliance with minimal overhead.

#### Acceptance Criteria

1. WHEN accessing the Software Components page THEN the system SHALL display a list of key components: application version, firmware binaries, STM32CubeProgrammer version, and configuration files
2. WHEN the application starts THEN the system SHALL automatically scan and record file versions, checksums (MD5), and last modified dates for all tracked components
3. WHEN viewing component details THEN the system SHALL show current version, file path, checksum, and last update date in a simple table format
4. WHEN exporting component information THEN the system SHALL generate a simple CSV report with component name, version, checksum, and date
5. WHEN firmware files are updated THEN the system SHALL detect file changes and prompt operator to update version information following SWE checklist
6. WHEN generating compliance reports THEN the system SHALL include a basic software manifest with all component versions and checksums

### Requirement 22: System Initialization and Graceful Shutdown

**User Story:** As a production operator, I want the system to start up reliably and shut down gracefully, so that no data is lost and the system is always in a consistent state.

#### Acceptance Criteria

1. WHEN the system starts THEN it SHALL perform initialization sequence validation including database connectivity, hardware enumeration, and configuration file integrity
2. WHEN initialization fails THEN the system SHALL display specific error messages and prevent production operations until issues are resolved
3. WHEN the system shuts down THEN it SHALL complete any in-progress operations, save current state, and close all hardware connections gracefully
4. WHEN unexpected shutdown occurs THEN the system SHALL detect incomplete operations on restart and provide recovery options
5. WHEN database is unavailable during startup THEN the system SHALL operate in offline mode with local caching until connectivity is restored
6. WHEN hardware is unavailable during startup THEN the system SHALL continue initialization but disable affected production steps with clear status indication

### Requirement 23: UST Brand Color Scheme and UI Design Standards

**User Story:** As a system administrator, I want the application to follow UST brand color standards, so that the factory tool maintains consistent corporate branding and professional appearance.

#### Acceptance Criteria

1. WHEN designing the user interface THEN the system SHALL use UST primary color Dark Teal (#006E74) for backgrounds and primary elements
2. WHEN displaying active states THEN the system SHALL use Light Teal (#0097AC) for progress indicators, active buttons, and highlights
3. WHEN showing text content THEN the system SHALL use White (#FFFFFF) text on dark backgrounds and Soft Black (#231F20) text on light backgrounds
4. WHEN indicating status THEN the system SHALL use consistent color coding with sufficient contrast ratios (minimum 4.5:1) for accessibility compliance
5. WHEN designing forms and inputs THEN the system SHALL use Light Teal (#0097AC) borders for active fields and validation feedback
6. WHEN creating error messages THEN the system SHALL maintain UST color scheme while ensuring error visibility through appropriate contrast and iconography

### Requirement 24: Windows Driver and System Integration Resilience

**User Story:** As a production operator, I want the system to handle Windows driver issues and system conflicts gracefully, so that production continues despite common Windows environment problems.

#### Acceptance Criteria

1. WHEN ST-Link drivers conflict THEN the system SHALL detect driver version mismatches and provide specific instructions to reinstall STM32CubeProgrammer with correct drivers
2. WHEN Windows updates affect USB drivers THEN the system SHALL detect driver changes and automatically attempt device re-enumeration with fallback to manual driver reinstallation guidance
3. WHEN antivirus software blocks ST-Link operations THEN the system SHALL detect access denied errors and provide specific antivirus exclusion instructions for STM32CubeProgrammer.exe and device communication
4. WHEN multiple ST-Link devices are detected THEN the system SHALL enumerate all devices and allow operator selection of the correct programmer with clear device identification
5. WHEN USB power management interferes THEN the system SHALL detect USB selective suspend issues and provide instructions to disable power management for ST-Link and FT232 devices
6. WHEN COM port assignments change THEN the system SHALL automatically re-scan and re-establish FT232 connections without requiring application restart
7. WHEN Windows firewall blocks database connections THEN the system SHALL detect network connectivity issues and provide specific firewall configuration instructions

### Requirement 25: Hardware Integration Error Recovery and Diagnostics

**User Story:** As a production operator, I want comprehensive hardware diagnostics and recovery procedures, so that I can quickly resolve hardware issues without technical support.

#### Acceptance Criteria

1. WHEN ST-Link connection fails THEN the system SHALL execute diagnostic sequence: check USB connection, verify driver installation, test with STM32CubeProgrammer CLI, and provide step-by-step recovery instructions
2. WHEN FT232 UART communication fails THEN the system SHALL test COM port availability, baud rate settings, and provide cable connection verification steps with LED status indicators if available
3. WHEN PPK2 device is not detected THEN the system SHALL verify USB connection, check Nordic nRF Connect compatibility, and provide manual measurement entry as fallback with clear instructions
4. WHEN device programming fails repeatedly THEN the system SHALL implement progressive recovery: soft reset, hard reset, cable check, fixture inspection, and escalation to technical support
5. WHEN USB hub power issues occur THEN the system SHALL detect insufficient power conditions and recommend powered USB hub or direct PC connection
6. WHEN hardware conflicts arise THEN the system SHALL provide device manager access instructions and specific VID:PID identification for troubleshooting
7. WHEN fixture hardware degrades THEN the system SHALL track connection failure patterns and alert supervisors when failure rates exceed 5% per hour

### Requirement 26: Production Environment Defensive Programming

**User Story:** As a production manager, I want the system to handle real factory floor conditions defensively, so that operator errors and environmental factors don't disrupt production.

#### Acceptance Criteria

1. WHEN operators disconnect USB cables during operations THEN the system SHALL detect disconnection within 2 seconds, safely abort current operations, and guide operator through proper reconnection
2. WHEN network connections drop during database writes THEN the system SHALL complete local caching, retry with exponential backoff (1s, 2s, 4s, 8s), and maintain operation integrity
3. WHEN database becomes corrupted THEN the system SHALL detect corruption on startup, automatically restore from last known good backup, and alert administrator
4. WHEN disk space becomes low THEN the system SHALL monitor available space and alert operators when less than 1GB remains, with automatic log rotation
5. WHEN system memory is exhausted THEN the system SHALL implement memory monitoring and graceful degradation by disabling non-essential features while maintaining core production functionality
6. WHEN operators enter invalid data THEN the system SHALL provide immediate validation feedback with specific correction instructions and prevent progression until data is valid
7. WHEN environmental factors affect hardware THEN the system SHALL detect temperature-related USB issues and provide cooling recommendations when connection stability degrades

### Requirement 27: Database Schema and Migration Strategy

**User Story:** As a system administrator, I want a well-defined database schema with migration capabilities, so that system updates don't lose production data.

#### Acceptance Criteria

1. WHEN initializing database THEN the system SHALL create tables: devices (id, serial_number, dev_eui, region, created_date), production_records (device_id, operator_id, firmware_version, test_results, timestamp), and system_config (version, settings, updated_date)
2. WHEN upgrading application versions THEN the system SHALL execute database migrations automatically with backup creation before any schema changes
3. WHEN database schema changes THEN the system SHALL validate migration success and rollback to backup if validation fails
4. WHEN concurrent access occurs THEN the system SHALL implement row-level locking using SQLite WAL mode with busy timeout of 30 seconds
5. WHEN database grows large THEN the system SHALL implement automatic archiving of records older than 1 year to separate archive database
6. WHEN backup operations run THEN the system SHALL create daily backups automatically and maintain 30 days of backup history with compression
7. WHEN data integrity issues are detected THEN the system SHALL run PRAGMA integrity_check on startup and alert administrator if corruption is found

### Requirement 28: STM32CubeProgrammer Integration and Error Handling

**User Story:** As a production operator, I want reliable firmware programming with clear error resolution, so that programming failures are quickly resolved without technical expertise.

#### Acceptance Criteria

1. WHEN STM32CubeProgrammer CLI returns error codes THEN the system SHALL map specific exit codes to user actions: code 1 (connection failed) → check cables, code 2 (programming failed) → retry with erase, code 3 (verification failed) → check firmware file
2. WHEN programming timeout occurs THEN the system SHALL implement 60-second timeout per programming operation with automatic retry using full chip erase before second attempt
3. WHEN firmware file validation fails THEN the system SHALL check file size (must be 50KB-512KB), verify .hex format, and validate checksum against known good firmware
4. WHEN chip protection is detected THEN the system SHALL execute mass erase command and retry programming with clear operator notification about protection removal
5. WHEN ST-Link firmware is outdated THEN the system SHALL detect version mismatch and provide specific instructions to update ST-Link firmware using STM32CubeProgrammer
6. WHEN multiple programming attempts fail THEN the system SHALL escalate to supervisor notification after 3 consecutive failures on same device
7. WHEN programming succeeds THEN the system SHALL verify by reading back first 1KB of flash memory and comparing checksum with expected values

### Requirement 29: Network Resilience and Offline Operation Enhancement

**User Story:** As a production operator, I want robust offline operation that handles all network failure scenarios, so that production never stops due to connectivity issues.

#### Acceptance Criteria

1. WHEN network connectivity is intermittent THEN the system SHALL implement connection health monitoring with 30-second ping tests and automatic offline mode activation
2. WHEN database server is unreachable THEN the system SHALL switch to local SQLite within 5 seconds and queue all operations for later synchronization
3. WHEN local cache exceeds capacity THEN the system SHALL compress older records and alert operator when 80% of 10,000 record limit is reached
4. WHEN synchronization conflicts occur THEN the system SHALL implement conflict resolution: local timestamp wins for device records, server timestamp wins for configuration changes
5. WHEN partial synchronization fails THEN the system SHALL maintain transaction integrity and retry failed records individually rather than re-sending entire batch
6. WHEN network recovery is detected THEN the system SHALL automatically begin background synchronization without interrupting current production operations
7. WHEN offline operation exceeds 24 hours THEN the system SHALL alert supervisor and recommend immediate synchronization to prevent data loss

### Requirement 30: Operator Skill Level Adaptation and Training Support

**User Story:** As a production supervisor, I want the system to adapt to different operator skill levels and provide training support, so that both experienced and new operators can work effectively.

#### Acceptance Criteria

1. WHEN new operators use the system THEN it SHALL provide "Guided Mode" with detailed step-by-step instructions and confirmation prompts for each action
2. WHEN experienced operators work THEN the system SHALL offer "Expert Mode" with streamlined interface and keyboard shortcuts for faster operation
3. WHEN errors occur frequently THEN the system SHALL track operator error patterns and suggest additional training for specific procedures
4. WHEN training mode is active THEN the system SHALL simulate full workflow without actual device programming, allowing practice with realistic feedback
5. WHEN operators need help THEN the system SHALL provide context-sensitive help with screenshots and video links for complex procedures
6. WHEN shift changes occur THEN the system SHALL provide handoff summary showing current production status, pending issues, and operator notes
7. WHEN performance metrics decline THEN the system SHALL suggest break reminders and ergonomic tips to maintain operator effectiveness colors: Dark Teal (#006E74) for backgrounds, Light Teal (#0097AC) for active elements, White (#FFFFFF) for text, and Soft Black (#231F20) for secondary text
2. WHEN displaying status indicators THEN the system SHALL use consistent color coding: Dark Teal for normal operations, Light Teal for active/in-progress states, and appropriate high-contrast colors for warnings and errors
3. WHEN creating forms and input fields THEN the system SHALL use Light Teal (#0097AC) borders for active fields and maintain proper contrast ratios for accessibility compliance
4. WHEN designing buttons and interactive elements THEN the system SHALL follow UST brand guidelines with consistent spacing, typography, and color application
5. WHEN displaying the application logo THEN the system SHALL use official UST branding assets and maintain proper logo placement and sizing standards

### Requirement 24: Hardware Driver Management and System Resilience

**User Story:** As a production operator, I want the system to handle Windows driver issues and hardware conflicts gracefully, so that production continues even when Windows behaves unpredictably.

#### Acceptance Criteria

1. WHEN ST-Link drivers conflict THEN the system SHALL detect driver version mismatches by checking STM32CubeProgrammer CLI output and provide specific driver update instructions with download links
2. WHEN Windows updates affect USB drivers THEN the system SHALL detect driver changes by monitoring Windows Device Manager events and automatically re-enumerate hardware connections
3. WHEN antivirus software blocks ST-Link operations THEN the system SHALL detect access denied errors (exit code 1 from STM32CubeProgrammer) and display specific antivirus exclusion instructions
4. WHEN USB hub power management interferes THEN the system SHALL detect USB device reset events and automatically retry hardware initialization up to 5 times with 2-second delays
5. WHEN multiple ST-Link devices are connected THEN the system SHALL enumerate all ST-Link devices and allow operator selection or automatically select the first available device
6. WHEN COM port enumeration fails THEN the system SHALL use Windows Registry queries to identify FT232 devices and provide manual COM port selection if automatic detection fails
7. WHEN hardware becomes unresponsive THEN the system SHALL implement hardware reset procedures including USB device reset commands and ST-Link power cycling instructions

### Requirement 25: Production Environment Hardening and Defensive Programming

**User Story:** As a production manager, I want the system to be resilient against common factory floor issues, so that production maintains high uptime despite environmental challenges.

#### Acceptance Criteria

1. WHEN network connectivity is intermittent THEN the system SHALL implement connection pooling with 30-second keepalive pings and automatic reconnection with exponential backoff (1s, 2s, 4s, 8s, max 30s)
2. WHEN database writes fail due to disk space THEN the system SHALL detect low disk space conditions (<1GB free) and automatically archive old log files while alerting operators
3. WHEN operators disconnect USB cables during operations THEN the system SHALL detect hardware disconnection within 1 second and safely abort current operations without corrupting device state
4. WHEN Windows decides to install updates THEN the system SHALL detect pending restart notifications and prevent new device workflows while allowing current operations to complete
5. WHEN system memory usage grows THEN the system SHALL monitor memory consumption and automatically restart background processes when usage exceeds 500MB
6. WHEN temporary files accumulate THEN the system SHALL automatically clean temporary directories older than 24 hours and maintain maximum 100MB of temporary storage
7. WHEN multiple application instances are started THEN the system SHALL use file locking to prevent concurrent access and display clear error messages for duplicate instances

### Requirement 26: STM32CubeProgrammer Integration and Error Code Mapping

**User Story:** As a production operator, I want clear explanations when programming fails, so that I can take appropriate corrective action without technical expertise.

#### Acceptance Criteria

1. WHEN STM32CubeProgrammer returns exit code 1 THEN the system SHALL display "Connection Failed - Check device insertion and ST-Link connection" with visual fixture diagram
2. WHEN STM32CubeProgrammer returns exit code 2 THEN the system SHALL display "Programming Failed - Device may be damaged or firmware file corrupted" and suggest device replacement
3. WHEN STM32CubeProgrammer returns exit code 3 THEN the system SHALL display "Verification Failed - Programming incomplete" and automatically retry programming operation
4. WHEN STM32CubeProgrammer hangs for >30 seconds THEN the system SHALL terminate the process and display "Programmer Timeout - Reset fixture and try again"
5. WHEN firmware file is missing or corrupted THEN the system SHALL validate file existence, size (>50KB, <512KB), and basic hex file format before attempting programming
6. WHEN ST-Link firmware is outdated THEN the system SHALL detect version incompatibility from CLI output and provide ST-Link firmware update instructions
7. WHEN programming operations succeed THEN the system SHALL verify success by reading device ID and first 32 bytes of programmed memory to confirm firmware presence

### Requirement 27: Database Schema Definition and Migration Strategy

**User Story:** As a system administrator, I want a clearly defined database structure with upgrade capabilities, so that data integrity is maintained as the system evolves.

#### Acceptance Criteria

1. WHEN initializing database THEN the system SHALL create tables: devices (id, serial_number, dev_eui, region, created_at, operator_id, status), test_results (device_id, test_type, result, timestamp), and system_config (key, value, updated_at)
2. WHEN database schema changes THEN the system SHALL implement versioned migrations with rollback capability and backup creation before applying changes
3. WHEN concurrent access occurs THEN the system SHALL use SQLite WAL mode with busy timeout of 5 seconds and implement retry logic for locked database conditions
4. WHEN database corruption is detected THEN the system SHALL automatically create backup copy, attempt repair using SQLite integrity check, and fallback to read-only mode if repair fails
5. WHEN exporting production data THEN the system SHALL generate SQL dump files with schema version information and data validation checksums
6. WHEN importing historical data THEN the system SHALL validate data format, check for duplicate serial numbers, and provide conflict resolution options
7. WHEN database grows large THEN the system SHALL implement automatic archiving of records older than 1 year while maintaining referential integrity

### Requirement 28: JSON Configuration Schema and Validation Framework

**User Story:** As a system administrator, I want robust configuration validation with clear error messages, so that configuration errors are caught before they affect production.

#### Acceptance Criteria

1. WHEN loading TTN credentials THEN the system SHALL validate against JSON schema requiring: application_name (string, 1-50 chars), join_eui (string, 16 hex chars), app_key (string, 32 hex chars), region (enum: US915, EU868, IN865)
2. WHEN JSON parsing fails THEN the system SHALL display line number and character position of syntax errors with suggested corrections
3. WHEN required fields are missing THEN the system SHALL list all missing required fields and provide example values for each field type
4. WHEN field values are invalid THEN the system SHALL validate hex string formats, enumerate valid region codes, and check string length constraints with specific error messages
5. WHEN configuration files are updated THEN the system SHALL create timestamped backup copies in .config/backups/ directory before applying changes
6. WHEN loading firmware configuration THEN the system SHALL validate firmware file paths exist, have correct extensions (.hex, .bin), and match configured region settings
7. WHEN exporting configuration THEN the system SHALL generate validated JSON with proper formatting and include metadata comments for documentation

### Requirement 29: Operator Skill Level Adaptation and Training Support

**User Story:** As a production trainer, I want the system to adapt to different operator skill levels, so that both experienced and new operators can work effectively.

#### Acceptance Criteria

1. WHEN new operators use the system THEN it SHALL provide "Guided Mode" with detailed step-by-step instructions and visual confirmation prompts for each action
2. WHEN experienced operators work THEN the system SHALL offer "Expert Mode" with streamlined interface showing only essential status information and minimal prompts
3. WHEN errors occur THEN the system SHALL provide skill-appropriate explanations: detailed troubleshooting steps for beginners, concise error codes for experts
4. WHEN training new operators THEN the system SHALL include "Practice Mode" that simulates full workflow without actual hardware programming or database writes
5. WHEN operators make repeated mistakes THEN the system SHALL detect error patterns and suggest additional training or supervisor assistance
6. WHEN shift changes occur THEN the system SHALL provide operator handoff summary showing current production status, any pending issues, and shift statistics
7. WHEN help is needed THEN the system SHALL provide context-sensitive help with visual diagrams, video links, and escalation procedures for technical support

### Requirement 30: Network Firewall and Security Infrastructure Compatibility

**User Story:** As an IT administrator, I want the system to work within corporate security policies, so that factory tools don't compromise network security or require security exceptions.

#### Acceptance Criteria

1. WHEN operating behind corporate firewalls THEN the system SHALL use standard HTTP/HTTPS ports (80, 443) for database synchronization and avoid non-standard port requirements
2. WHEN proxy servers are required THEN the system SHALL support HTTP proxy configuration with authentication and automatic proxy detection from Windows settings
3. WHEN SSL certificates are corporate-managed THEN the system SHALL validate certificates against Windows certificate store and support corporate certificate authorities
4. WHEN network monitoring is active THEN the system SHALL minimize network traffic by using compressed data transfer and batch synchronization operations
5. WHEN VPN connections are required THEN the system SHALL detect VPN connectivity status and gracefully handle VPN disconnections with automatic retry logic
6. WHEN DNS resolution fails THEN the system SHALL fallback to IP address connections and cache successful connection parameters for future use
7. WHEN security scanning tools inspect traffic THEN the system SHALL use standard protocols and avoid encrypted payloads that might trigger security alerts color palette: Dark Teal (#006E74), Light Teal (#0097AC), Soft Black (#231F20), and White (#FFFFFF)
2. WHEN displaying application backgrounds THEN the system SHALL use Dark Teal (#006E74) for primary backgrounds and Light Teal (#0097AC) for secondary backgrounds and highlights
3. WHEN showing status indicators THEN the system SHALL use Dark Teal for normal operations, Light Teal for active/selected states, Soft Black for text, and White for contrast elements
4. WHEN designing buttons and controls THEN the system SHALL use Light Teal (#0097AC) for primary action buttons and Dark Teal (#006E74) for secondary actions
5. WHEN displaying text content THEN the system SHALL use Soft Black (#231F20) for primary text on light backgrounds and White (#FFFFFF) for text on dark teal backgrounds
6. WHEN creating error states THEN the system SHALL maintain UST color harmony by using teal variants with appropriate contrast ratios for accessibility
7. WHEN generating reports and labels THEN the system SHALL incorporate UST brand colors while ensuring readability and professional appearance

### Requirement 24: Audit Trail and Compliance Reporting

**User Story:** As a compliance officer, I want complete audit trails of all production activities, so that we can demonstrate regulatory compliance and investigate any issues.

#### Acceptance Criteria

1. WHEN any operation occurs THEN the system SHALL log the action with operator ID, timestamp, device ID, and result
2. WHEN generating compliance reports THEN the system SHALL include all required traceability data for regulatory submissions
3. WHEN data is modified THEN the system SHALL maintain immutable audit records that cannot be altered
4. IF audit data is queried THEN the system SHALL provide search and filtering capabilities with export options
5. WHEN retention periods expire THEN the system SHALL archive old audit data while maintaining accessibility
6. WHEN security events occur THEN the system SHALL log authentication attempts, configuration changes, and data access
#
## Requirement 24: Hardware Driver Management and System Resilience

**User Story:** As a production operator, I want the system to handle Windows driver issues and hardware conflicts gracefully, so that production continues even when Windows behaves unpredictably.

#### Acceptance Criteria

1. WHEN ST-Link drivers conflict THEN the system SHALL detect driver version mismatches by checking STM32CubeProgrammer CLI output and provide specific driver update instructions with download links
2. WHEN Windows updates affect USB drivers THEN the system SHALL detect driver changes by monitoring Windows Device Manager events and automatically re-enumerate hardware connections
3. WHEN antivirus software blocks ST-Link operations THEN the system SHALL detect access denied errors (exit code 1 from STM32CubeProgrammer) and display specific antivirus exclusion instructions
4. WHEN USB hub power management interferes THEN the system SHALL detect USB device reset events and automatically retry hardware initialization up to 5 times with 2-second delays
5. WHEN multiple ST-Link devices are connected THEN the system SHALL enumerate all ST-Link devices and allow operator selection or automatically select the first available device
6. WHEN COM port enumeration fails THEN the system SHALL use Windows Registry queries to identify FT232 devices and provide manual COM port selection if automatic detection fails
7. WHEN hardware becomes unresponsive THEN the system SHALL implement hardware reset procedures including USB device reset commands and ST-Link power cycling instructions

### Requirement 25: Production Environment Hardening and Defensive Programming

**User Story:** As a production manager, I want the system to be resilient against common factory floor issues, so that production maintains high uptime despite environmental challenges.

#### Acceptance Criteria

1. WHEN network connectivity is intermittent THEN the system SHALL implement connection pooling with 30-second keepalive pings and automatic reconnection with exponential backoff (1s, 2s, 4s, 8s, max 30s)
2. WHEN database writes fail due to disk space THEN the system SHALL detect low disk space conditions (<1GB free) and automatically archive old log files while alerting operators
3. WHEN operators disconnect USB cables during operations THEN the system SHALL detect hardware disconnection within 1 second and safely abort current operations without corrupting device state
4. WHEN Windows decides to install updates THEN the system SHALL detect pending restart notifications and prevent new device workflows while allowing current operations to complete
5. WHEN system memory usage grows THEN the system SHALL monitor memory consumption and automatically restart background processes when usage exceeds 500MB
6. WHEN temporary files accumulate THEN the system SHALL automatically clean temporary directories older than 24 hours and maintain maximum 100MB of temporary storage
7. WHEN multiple application instances are started THEN the system SHALL use file locking to prevent concurrent access and display clear error messages for duplicate instances

### Requirement 26: STM32CubeProgrammer Integration and Error Code Mapping

**User Story:** As a production operator, I want clear explanations when programming fails, so that I can take appropriate corrective action without technical expertise.

#### Acceptance Criteria

1. WHEN STM32CubeProgrammer returns exit code 1 THEN the system SHALL display "Connection Failed - Check device insertion and ST-Link connection" with visual fixture diagram
2. WHEN STM32CubeProgrammer returns exit code 2 THEN the system SHALL display "Programming Failed - Device may be damaged or firmware file corrupted" and suggest device replacement
3. WHEN STM32CubeProgrammer returns exit code 3 THEN the system SHALL display "Verification Failed - Programming incomplete" and automatically retry programming operation
4. WHEN STM32CubeProgrammer hangs for >30 seconds THEN the system SHALL terminate the process and display "Programmer Timeout - Reset fixture and try again"
5. WHEN firmware file is missing or corrupted THEN the system SHALL validate file existence, size (>50KB, <512KB), and basic hex file format before attempting programming
6. WHEN ST-Link firmware is outdated THEN the system SHALL detect version incompatibility from CLI output and provide ST-Link firmware update instructions
7. WHEN programming operations succeed THEN the system SHALL verify success by reading device ID and first 32 bytes of programmed memory to confirm firmware presence

### Requirement 27: Database Schema Definition and Migration Strategy

**User Story:** As a system administrator, I want a clearly defined database structure with upgrade capabilities, so that data integrity is maintained as the system evolves.

#### Acceptance Criteria

1. WHEN initializing database THEN the system SHALL create tables: devices (id, serial_number, dev_eui, region, created_at, operator_id, status), test_results (device_id, test_type, result, timestamp), and system_config (key, value, updated_at)
2. WHEN database schema changes THEN the system SHALL implement versioned migrations with rollback capability and backup creation before applying changes
3. WHEN concurrent access occurs THEN the system SHALL use SQLite WAL mode with busy timeout of 5 seconds and implement retry logic for locked database conditions
4. WHEN database corruption is detected THEN the system SHALL automatically create backup copy, attempt repair using SQLite integrity check, and fallback to read-only mode if repair fails
5. WHEN exporting production data THEN the system SHALL generate SQL dump files with schema version information and data validation checksums
6. WHEN importing historical data THEN the system SHALL validate data format, check for duplicate serial numbers, and provide conflict resolution options
7. WHEN database grows large THEN the system SHALL implement automatic archiving of records older than 1 year while maintaining referential integrity

### Requirement 28: JSON Configuration Schema and Validation Framework

**User Story:** As a system administrator, I want robust configuration validation with clear error messages, so that configuration errors are caught before they affect production.

#### Acceptance Criteria

1. WHEN loading TTN credentials THEN the system SHALL validate against JSON schema requiring: application_name (string, 1-50 chars), join_eui (string, 16 hex chars), app_key (string, 32 hex chars), region (enum: US915, EU868, IN865)
2. WHEN JSON parsing fails THEN the system SHALL display line number and character position of syntax errors with suggested corrections
3. WHEN required fields are missing THEN the system SHALL list all missing required fields and provide example values for each field type
4. WHEN field values are invalid THEN the system SHALL validate hex string formats, enumerate valid region codes, and check string length constraints with specific error messages
5. WHEN configuration files are updated THEN the system SHALL create timestamped backup copies in .config/backups/ directory before applying changes
6. WHEN loading firmware configuration THEN the system SHALL validate firmware file paths exist, have correct extensions (.hex, .bin), and match configured region settings
7. WHEN exporting configuration THEN the system SHALL generate validated JSON with proper formatting and include metadata comments for documentation

### Requirement 29: Operator Skill Level Adaptation and Training Support

**User Story:** As a production trainer, I want the system to adapt to different operator skill levels, so that both experienced and new operators can work effectively.

#### Acceptance Criteria

1. WHEN new operators use the system THEN it SHALL provide "Guided Mode" with detailed step-by-step instructions and visual confirmation prompts for each action
2. WHEN experienced operators work THEN the system SHALL offer "Expert Mode" with streamlined interface showing only essential status information and minimal prompts
3. WHEN errors occur THEN the system SHALL provide skill-appropriate explanations: detailed troubleshooting steps for beginners, concise error codes for experts
4. WHEN training new operators THEN the system SHALL include "Practice Mode" that simulates full workflow without actual hardware programming or database writes
5. WHEN operators make repeated mistakes THEN the system SHALL detect error patterns and suggest additional training or supervisor assistance
6. WHEN shift changes occur THEN the system SHALL provide operator handoff summary showing current production status, any pending issues, and shift statistics
7. WHEN help is needed THEN the system SHALL provide context-sensitive help with visual diagrams, video links, and escalation procedures for technical support

### Requirement 30: Network Firewall and Security Infrastructure Compatibility

**User Story:** As an IT administrator, I want the system to work within corporate security policies, so that factory tools don't compromise network security or require security exceptions.

#### Acceptance Criteria

1. WHEN operating behind corporate firewalls THEN the system SHALL use standard HTTP/HTTPS ports (80, 443) for database synchronization and avoid non-standard port requirements
2. WHEN proxy servers are required THEN the system SHALL support HTTP proxy configuration with authentication and automatic proxy detection from Windows settings
3. WHEN SSL certificates are corporate-managed THEN the system SHALL validate certificates against Windows certificate store and support corporate certificate authorities
4. WHEN network monitoring is active THEN the system SHALL minimize network traffic by using compressed data transfer and batch synchronization operations
5. WHEN VPN connections are required THEN the system SHALL detect VPN connectivity status and gracefully handle VPN disconnections with automatic retry logic
6. WHEN DNS resolution fails THEN the system SHALL fallback to IP address connections and cache successful connection parameters for future use
7. WHEN security scanning tools inspect traffic THEN the system SHALL use standard protocols and avoid encrypted payloads that might trigger security alerts
COLDRA Factory Tool - Design Document
Overview
The COLDRA Factory Production Tool is a Windows desktop application designed for high-throughput manufacturing of RAK3172-based LoRaWAN temperature sensors. The system implements a robust, fault-tolerant architecture that handles real factory floor conditions including hardware failures, network interruptions, and operator variability.

Core Design Principles
Defensive Programming: Assume hardware will fail and networks will disconnect.
Operator-Centric UX: Design for varying skill levels with clear visual feedback.
Production Resilience: Never lose data, always provide recovery paths.
Performance Optimization: Target 60-90 second cycle times with concurrent operations.
Security by Design: Encrypt credentials, audit all operations, role-based access.
System Architecture
(Assuming the architecture diagram and component list from the previous design discussion is implied here or would precede this section)

The system is composed of several key components:

State Machine Controller: Orchestrates the overall production workflow and hardware interaction states.
Hardware Manager: Handles USB device detection, enumeration, and communication with ST-Link, FT232, and PPK2.
Firmware Manager: Manages firmware file selection, validation, and programming via STM32CubeProgrammer CLI.
Provisioning Manager: Handles TTN credential provisioning via AT commands over FT232 UART.
Test Manager: Executes LoRaWAN join tests and other functional verifications via AT commands.
Power Measurement Manager: Guides operator for manual PPK2 entry and validates ranges.
Label Generator: Creates QR codes and exports labels (PDF/ZPL).
Data Manager: Manages the local SQLite database, transactions, caching, and synchronization.
UI Manager: Renders the WEB interface, manages views, and handles user input.
Configuration Manager: Loads, validates, and manages application configuration.
Software Component Tracker: Monitors and records versions/checksums of key software elements.
Detailed Component Design
1. State Machine Controller
1.1 Production Workflow State Machine
Manages the high-level steps for processing a device.

States:
Idle, SerialNumberGeneration, Programming, Provisioning, Testing, PowerMeasurement, LabelGeneration, DatabaseLogging, Completed, Error, Recovery, Shutdown.

Transitions & Logic (Pseudocode/Structure):

// State transition logic within StateMachineController
private async Task<StateTransitionResult> ExecuteStateAsync(ProductionState state)
{
    return state switch
    {
        ProductionState.Idle => await HandleIdleAsync(),
        ProductionState.SerialNumberGeneration => await ExecuteSerialNumberGenerationAsync(),
        ProductionState.Programming => await ExecuteProgrammingAsync(),
        ProductionState.Provisioning => await ExecuteProvisioningAsync(),
        ProductionState.Testing => await ExecuteTestingAsync(),
        ProductionState.PowerMeasurement => await ExecutePowerMeasurementAsync(),
        ProductionState.LabelGeneration => await ExecuteLabelGenerationAsync(),
        ProductionState.DatabaseLogging => await ExecuteDatabaseLoggingAsync(),
        ProductionState.Completed => await HandleCompletionAsync(),
        ProductionState.Error => await HandleErrorAsync(),
        ProductionState.Recovery => await ExecuteRecoveryAsync(),
        ProductionState.Shutdown => await HandleShutdownAsync(),
        _ => StateTransitionResult.Failure("Unknown state")
    };
}

1.2 Hardware State Machine
Manages the detection and connection status of required hardware.

States:
Unknown, Disconnected, StLinkDetected, Ft232Detected, BothConnected, Error.

Transitions & Logic (Pseudocode/Structure):
// Hardware state logic within HardwareManager
private async Task<HardwareState> DetectHardwareStateAsync()
{
    bool stLinkFound = await DetectStLinkAsync();
    bool ft232Found = await DetectFt232Async();

    if (stLinkFound && ft232Found)
        return HardwareState.BothConnected;
    else if (stLinkFound)
        return HardwareState.StLinkDetected;
    else if (ft232Found)
        return HardwareState.Ft232Detected;
    else
        return HardwareState.Disconnected;
}

// Called periodically or on USB events
private async Task<bool> ValidateBothConnectedAsync()
{
    var deviceIdResult = await _stLinkController.GetDeviceIdAsync();
    if (!deviceIdResult.Success)
    {
        // Log error, potentially trigger re-detection or error state
        _logger.LogError($"ST-Link validation failed: {deviceIdResult.ErrorMessage}");
        return false; // Or trigger specific recovery/error handling
    }

    var comPortResult = await _ft232Controller.GetComPortAsync();
    if (!comPortResult.Success)
    {
        // Log error, potentially trigger re-detection or error state
        _logger.LogError($"FT232 validation failed: {comPortResult.ErrorMessage}");
        return false; // Or trigger specific recovery/error handling
    }

    // Additional validation like sending AT command can be done here
    var atTestResult = await _ft232Controller.SendAtCommandAsync("AT\r\n");
    if (!atTestResult.Success || !atTestResult.Response.Contains("OK"))
    {
         _logger.LogWarning($"FT232 AT test failed: {atTestResult.ErrorMessage}");
         // Decide if this is a critical failure or just a warning
         // For now, let's assume connected hardware is sufficient for state
    }

    return true; // Both hardware components appear accessible
}

2. Hardware Manager
2.1 USB Device Detection and Enumeration
Responsibilities:

Enumerate connected USB devices on startup and periodically.
Identify ST-Link (VID: 0483, PIDs: 374B, 3748) and FT232 (VID: 0403, PID: 6001).
Detect PPK2 (VID: 1915, PID: 521F).
Monitor for device removal (within 3 seconds).
Implementation Details:

Use Windows Management Instrumentation (WMI) or ManagementEventWatcher to monitor Win32_PnPEntity for arrival/removal events.
Filter events based on VID/PID.
Maintain a list of currently connected devices and their properties (COM ports for FT232).
Trigger state machine updates or error handling when device status changes.
2.2 ST-Link Validation
Responsibilities:

Validate that the detected ST-Link can communicate with a device.
Implementation Details:

Execute STM32CubeProgrammer_CLI.exe -c port=SWD (or similar connection command).
Parse the output to confirm successful connection (e.g., finding device ID).
Handle specific exit codes and map them to user-friendly messages (leveraging StLinkErrorMapper).
2.3 FT232 COM Port Identification & Validation
Responsibilities:

Identify the COM port associated with the detected FT232.
Test basic UART communication.
Implementation Details:

Query Windows registry or WMI (Win32_SerialPort) using the FT232 instance ID to find the COM port name.
Open a serial connection at 115200 baud.
Send AT\r\n command.
Expect OK response within 1 second timeout.
Close connection after validation.
3. Serial Number Generator
3.1 Unique Serial Number Generation with Atomicity
Responsibilities:

Generate serial numbers in the format DDMMYY-R-NNN.
Ensure atomic assignment to prevent duplicates, even under concurrent access or failure.
Implementation Details:

Database Table: SerialNumbers with columns: DateRegion (TEXT, e.g., "240815-US915"), LastNumber (INTEGER), MaxLimit (INTEGER, default 999).
Algorithm:
Calculate currentDateRegion (e.g., "240815-US915").
Start a database transaction.
SELECT LastNumber FROM SerialNumbers WHERE DateRegion = @currentDateRegion.
If no row exists, INSERT INTO SerialNumbers (DateRegion, LastNumber, MaxLimit) VALUES (@currentDateRegion, 0, 999).
Read the LastNumber.
If LastNumber >= MaxLimit, commit transaction and throw "Limit Exceeded" exception.
Increment LastNumber.
UPDATE SerialNumbers SET LastNumber = @newLastNumber WHERE DateRegion = @currentDateRegion.
Commit transaction.
Format and return the serial number string (e.g., 240815-US915-001).
Concurrency Handling: Database transaction with row-level locking ensures atomicity.
4. Firmware Manager
4.1 Firmware Programming & Validation (STM32CubeProgrammer Integration)
Implementation Details:

Validate firmware file path, size (50KB-256KB), and region match before programming.
Execute STM32CubeProgrammer_CLI.exe with appropriate arguments for flashing.
Capture exit codes and use StLinkErrorMapper for specific error handling.
public class StLinkErrorInfo
{
    public string Code { get; }
    public string Description { get; }
    public string[] SuggestedActions { get; }
    public bool IsRetryable { get; }

    public StLinkErrorInfo(string code, string description, string[] suggestedActions, bool isRetryable)
    {
        Code = code;
        Description = description;
        SuggestedActions = suggestedActions;
        IsRetryable = isRetryable;
    }
}

public static class StLinkErrorMapper
{
    private static readonly Dictionary<int, StLinkErrorInfo> ErrorMap = new()
    {
        [0] = new("SUCCESS", "Operation completed successfully.", Array.Empty<string>(), false),
        [1] = new("CONNECTION_FAILED", "Connection Failed - Check device insertion and ST-Link connection", new[] { "Verify device is correctly seated in the fixture.", "Check ST-Link USB cable connection.", "Ensure ST-Link firmware is up-to-date." }, false),
        [2] = new("PROGRAMMING_FAILED", "Programming Failed - Device may be damaged or firmware file corrupted", new[] { "Retry with full chip erase.", "Replace the device if the problem persists.", "Verify the firmware file integrity." }, true),
        [3] = new("VERIFICATION_FAILED", "Verification Failed - Programming incomplete", new[] { "Retry programming operation.", "Check for electrical noise or unstable power.", "Perform a full chip erase before retrying." }, true),
        [4] = new("TARGET_NOT_FOUND", "Target Device Not Found", new[] { "Check device insertion in fixture.", "Verify target voltage levels.", "Try a different ST-Link probe." }, false),
        [5] = new("PROTECTION_ERROR", "Device Protection Error", new[] { "Execute mass erase to remove write protection.", "Ensure correct target voltage is applied.", "Check for permanent protection settings." }, true),
        // ... (other error codes)
    };

    public static StLinkErrorInfo GetErrorInfo(int exitCode) => ErrorMap.GetValueOrDefault(exitCode, new("UNKNOWN_ERROR", $"An unknown error occurred (Exit Code: {exitCode}).", new[] { "Check system logs for detailed CLI output.", "Restart the ST-Link probe.", "Contact technical support if the issue persists." }, true));
}

public async Task<ProgrammingResult> ProgramDeviceAsync(string hexFilePath, string deviceId)
{
    if (!File.Exists(hexFilePath))
        return new(false, "FIRMWARE_FILE_NOT_FOUND", "Firmware file not found.", Array.Empty<string>(), false);

    var fileInfo = new FileInfo(hexFilePath);
    if (fileInfo.Length < 50 * 1024 || fileInfo.Length > 512 * 1024)
        return new(false, "INVALID_FIRMWARE_SIZE", "Firmware file size is outside the valid range (50KB-512KB).", Array.Empty<string>(), false);

    // Add region validation logic here if needed

    var args = $"-c port=SWD sn={deviceId} -w \"{hexFilePath}\" -v -s";
    var result = await _cliRunner.ExecuteAsync("STM32_Programmer_CLI.exe", args, timeoutMs: 30000); // 30s timeout

    if (result.ExitCode == 0)
    {
        // Post-programming verification
        var verifyResult = await VerifyProgrammingAsync(deviceId, hexFilePath);
        if (verifyResult.Success)
        {
            return new(true, "SUCCESS", "Device programmed and verified successfully.", Array.Empty<string>(), false);
        }
        else
        {
            return verifyResult; // Return the verification failure details
        }
    }
    else
    {
        var errorInfo = StLinkErrorMapper.GetErrorInfo(result.ExitCode);
        return new(false, errorInfo.Code, errorInfo.Description, errorInfo.SuggestedActions, errorInfo.IsRetryable);
    }
}

private async Task<ProgrammingResult> VerifyProgrammingAsync(string deviceId, string hexFilePath)
{
     // Example: Read device ID
     var args = $"-c port=SWD sn={deviceId} -r 0x1FFF7590 12"; // Read 12 bytes from UID base address
     var readResult = await _cliRunner.ExecuteAsync("STM32_Programmer_CLI.exe", args, timeoutMs: 10000);

     if (readResult.ExitCode != 0)
     {
         return new(false, "VERIFICATION_READ_FAILED", "Failed to read device ID for verification.", new[] { "Retry verification.", "Check ST-Link connection." }, true);
     }

     // Example: Read first 32 bytes of flash
     args = $"-c port=SWD sn={deviceId} -r 0x08000000 32"; // Read 32 bytes from flash start
     readResult = await _cliRunner.ExecuteAsync("STM32_Programmer_CLI.exe", args, timeoutMs: 10000);

     if (readResult.ExitCode != 0)
     {
         return new(false, "VERIFICATION_READ_FAILED", "Failed to read flash memory for verification.", new[] { "Retry verification.", "Check ST-Link connection." }, true);
     }

     // Here you would compare `readResult.Output` with expected data (e.g., from the hex file)
     // For simplicity, let's assume a basic check passes.
     // A real implementation would parse the hex file and compare the relevant sections.

     // Placeholder for actual verification logic
     bool verificationPassed = true; // This should be the result of the comparison

     if (verificationPassed)
     {
         return new(true, "VERIFICATION_SUCCESS", "Programming verified by reading device ID and flash memory.", Array.Empty<string>(), false);
     }
     else
     {
         return new(false, "VERIFICATION_MISMATCH", "Flash memory content does not match the programmed firmware.", new[] { "Retry programming operation.", "Check for electrical noise or unstable power." }, true);
     }
}
5. Provisioning Manager
5.1 TTN Credential Provisioning via AT Commands
Responsibilities:

Read TTN credentials (JoinEUI, AppKey) from a validated JSON file.
Read DevEUI from the RAK3172 barcode (operator input or scanned).
Send AT commands to provision AppEUI and AppKey.
Save configuration to RUI3 storage.
Implementation Details:

JSON Validation: Use System.Text.Json with schema validation or manual checks for required fields (join_eui, app_key), types, and formats (16/32 hex chars).
AT Command Sequence (via FT232Controller):
AT+DEVEUI=? (Verify communication, get DevEUI for logging).
AT+APPEUI=<JoinEUI> (Send JoinEUI).
AT+APPKEY=<AppKey> (Send AppKey).
AT&W (Save configuration).
Implement timeouts (1s for most commands) and retry logic (e.g., 3 retries for AT&W).
Validate "OK" responses for each command.
Handle specific provisioning errors (e.g., if device rejects key format).
6. Test Manager
6.1 Device Testing via AT Commands
Responsibilities:

Execute LoRaWAN join test.
Verify firmware version, band, mask.
Test credential storage.
Test temperature sensor, battery voltage.
Implementation Details:

AT Command Sequence (via FT232Controller):
AT+JOIN (with 90s timeout, retry logic). Handle "NOT_TESTED" if gateway is unavailable (determined by prior connectivity check or timeout interpretation).
AT+VER=? (Expect "2.2.0").
AT+BAND=?, AT+MASK=? (Verify regional settings).
AT+APPEUI=? (Verify provisioned AppEUI is readable).
AT+TEMP=? (Parse response, check 15-35°C range).
AT+BAT=? (Parse response, check 2000-4000mV range).
Implement appropriate timeouts and retry logic for each command.
Parse responses and validate against expected criteria.
Record test results (Pass/Fail/Not_Tested) for database logging.
7. Power Measurement Manager
7.1 Manual Power Measurement Workflow
Responsibilities:

Prompt operator for manual PPK2 measurements.
Provide input fields for Ultra-Low Power (1-10µA) and Active Mode (10-50mA) readings.
Validate entered values against specified ranges.
Implementation Details:

UI component in the main window for data entry.
Input validation: Check if entered values are numeric and within ranges (1-10 for UL, 10-50 for Active).
Display clear error messages if values are out of range.
Store validated measurements in the production data context for logging.
8. Label Generator
8.1 QR Code Generation and Export
Responsibilities:

Generate QR codes containing serial number, DevEUI, firmware version, manufacturing date.
Export labels in PDF, ZPL, or other supported formats.
Implementation Details:

Use a library like QRCoder or ZXing.Net to generate QR code bitmaps from the required data string.
Create label templates/layout.
Implement export logic for:
PDF: Use iTextSharp or similar to create a PDF with the QR code and text.
ZPL: Generate Zebra Programming Language string with QR code and text commands.
Implement retry logic for QR code generation errors (e.g., different error correction levels).
Implement print job queuing if printer is unavailable, processing queue when printer reconnects.
Verify print completion (if API allows) and update device status.
PDF Export Detail: Ensure generated PDFs are high-resolution suitable for manual printing.
9. Data Manager
9.1 Database Operations and Management
Responsibilities:

Manage SQLite database connection.
Perform ACID transactions for device logging.
Handle concurrent access with row-level locking/optimistic locking.
Implement local caching for offline operation.
Manage synchronization with central database/server when online.
Implementation Details:

Use System.Data.SQLite or Microsoft.Data.Sqlite.
Wrap database operations in TransactionScope or use BEGIN IMMEDIATE/COMMIT/ROLLBACK for ACID compliance.
Use SELECT ... FOR UPDATE or application-level locking mechanisms for row-level control if needed, or implement optimistic locking with version stamps/timestamps.
Offline Caching: Store pending operations (serialized objects or SQL commands) in a separate local table or file when database is unavailable.
Sync Logic: When connectivity is restored, replay cached operations. Handle conflicts (e.g., if a device was processed twice).
Database Schema: Define tables for Devices, SerialNumbers, ProductionLogs, AuditLogs, CachedOperations.
Example Devices table: Id (PK), SerialNumber (UNIQUE), DevEUI, FirmwareVersion, ManufacturingDate, Status, PowerUltraLow_uA, PowerActive_mA, TestResultsJson, LabelGenerated (bool), Timestamp.
Example ProductionLogs table: Id (PK), DeviceId (FK), Action (e.g., ProgrammingStarted, ProvisioningCompleted), Timestamp, Details (JSON).
Example AuditLogs table: Id (PK), UserId, Action, Timestamp, Details.
Migration Strategy: Implement a simple versioning system. On startup, check a SchemaVersion table. If the application version requires a newer schema, execute predefined migration scripts (SQL) to update the database structure.
Data Archiving: Implement automatic archiving of records older than 1 year while maintaining referential integrity.
Read Consistency: Use snapshot isolation or equivalent mechanisms (e.g., WAL mode in SQLite with appropriate read transactions) to ensure consistent reads during exports.
10. UI Manager
10.1 User Interface and Experience
Responsibilities:

Render the WEB application window.
Display dashboard with hardware status, production counts, workflow state.
Show step-by-step progress indicators.
Present error messages and recovery options.
Adapt UI based on operator skill level.
Implementation Details:

Dashboard (Req 23/17):
Background: Dark Teal (#006E74).
Text: White (#FFFFFF).
Elements: Hardware connection status icons/text, Daily production count/yield, Current workflow state.
Progress Indicators (Req 17):
Steps: "Insert Device" → "Programming" → "Provisioning" → "Testing" → "Remove Device".
Color: Light Teal (#0097AC).
Update dynamically as state machine progresses.
Error Display (Req 17/25):
Background/Border: Light Teal (#0097AC).
Text: Soft Black (#231F20).
Message: Plain language, specific actions.
Include visual aids (e.g., fixture diagram for ST-Link connection errors).
Skill Level Adaptation (Req 29):
Implement different UI modes (Guided, Standard, Expert).
Guided Mode: Show more tooltips, explanations, step-by-step wizards.
Standard Mode: Default view with standard controls.
Expert Mode: Show more detailed logs, advanced settings, keyboard shortcuts.
Practice Mode: Allow operators to run through the workflow without affecting real devices/data.
Help System (Req 29): Provide context-sensitive help with visual diagrams, video links, and escalation procedures for technical support.
Real-time Monitoring (Req 10): Display real-time counters, yield calculations, and performance alerts. Implement 5-second refresh for stats. Support CSV report generation for performance tracking.
Operator Performance Tracking (Req 10): Record individual operator statistics while maintaining privacy (e.g., associate stats with an operator ID internally but anonymize in reports).
11. Configuration Manager
11.1 Configuration Handling
Responsibilities:

Load application configuration from file.
Validate configuration settings (paths, formats).
Create backups before applying changes.
Export configuration.
Implementation Details:

Use appsettings.json or a custom JSON file.
Define a configuration class matching the expected structure.
Deserialize configuration on startup.
Validate critical settings (firmware paths, region codes).
Backup Creation: Before saving changes, copy the current config file to .config/backups/ with a timestamped name.
Export: Generate validated JSON with proper formatting and include metadata comments for documentation.
Validation Framework: Implement robust validation for JSON schema, hex string formats, valid region codes, string length constraints, firmware file paths/extensions/sizes, etc. Display specific error messages.
Semantic Versioning: Increment application version following MAJOR.MINOR.PATCH as per SWE guidelines when configuration changes.
Audit Logging: Log configuration changes with timestamp, operator ID, and description in the audit database.
Single Instance: Use file locking to prevent concurrent access and display clear error messages for duplicate instances.
12. Software Component Tracker
12.1 SWE Compliance Tracking
Responsibilities:

Scan and record versions, checksums (MD5), last modified dates of key components.
Display component information in a dedicated UI page.
Export component information to CSV.
Support SWE checklist prompts.
Implementation Details:

Component List: Application executable, firmware files (COLDRA_OTAA_UltraLowPower.hex), STM32CubeProgrammer_CLI.exe, key configuration files.
Scan Logic (on startup):
Get file version using FileVersionInfo.GetVersionInfo().
Calculate MD5 hash using System.Security.Cryptography.MD5.
Get LastWriteTime using FileInfo.LastWriteTime.
Store information in memory and/or a dedicated table/cache.
UI Page: Simple table view showing Name, Version, Checksum, Last Modified.
Export: Generate CSV with component details.
SWE Checklist Integration: When firmware files are detected as changed, trigger a prompt/event that can be linked to an external SWE checklist process/workflow.
Compliance Reports: Include a basic software manifest with all component versions and checksums.
13. System Initialization and Graceful Shutdown
13.1 Startup and Shutdown Sequences
Responsibilities:

Perform initialization checks (DB, Hardware, Config).
Handle initialization failures gracefully.
Ensure all operations complete or are safely cached on shutdown.
Close hardware connections cleanly.
Provide recovery options after unexpected shutdown.
Implementation Details:

Initialization Sequence:
Load and validate configuration.
Initialize database connection. If fails, enter offline mode.
Initialize hardware manager and start detection.
Initialize other managers.
If any critical initialization fails, display specific error and prevent production start.
Graceful Shutdown:
Signal state machine to stop accepting new devices.
Wait for current device operation to complete or reach a safe pause point.
Commit any pending database transactions.
Flush and sync local cache if needed.
Close serial/USB connections.
Save application state if necessary.
Recovery on Restart:
Check database for incomplete operations (e.g., devices in 'Programming' or 'Error' state from previous session).
Present recovery options to the operator (e.g., resume, discard, investigate).
14. Additional Considerations
14.1 Network Compatibility (Req 30)
Design network interactions (e.g., TTN API calls, future sync) to be compatible with standard corporate firewalls and proxies. Use common ports and protocols where possible.
14.2 Field Return Analysis (Req 14)
Implement a dedicated UI or function to enter a serial number and retrieve complete manufacturing history from the database for quality analysis.
14.3 Fixture Management (Req 16)
Enforce single fixture operation. Provide clear "Fixture Ready" status when all required hardware is connected. Offer troubleshooting guidance for specific hardware failures. Detect device removal and reset to IDLE state.
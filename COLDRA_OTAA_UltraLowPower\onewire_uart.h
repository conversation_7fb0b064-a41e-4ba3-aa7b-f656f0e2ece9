/**
 * UART-based OneWire Implementation for RAK3172
 * ZERO delayMicroseconds() - Hardware timing only
 * 
 * @version 1.0.0
 * @date 2025-09-06
 * 
 * This implementation uses UART hardware to generate precise OneWire timing
 * without any CPU blocking delays. The UART baud rate is set to create
 * the exact bit timing required by the OneWire protocol.
 * 
 * OneWire timing requirements:
 * - Reset pulse: 480µs low
 * - Presence detect: 70µs sampling window
 * - Write 1: 1-15µs low, 45-60µs high
 * - Write 0: 60-120µs low, 1-15µs high
 * - Read slot: 1-15µs low, then sample at 15µs
 * 
 * UART timing calculation:
 * - 9600 baud = 104µs per bit
 * - 0xF0 = 11110000 = 416µs low, 416µs high (close to 480µs reset)
 * - 0xFF = 11111111 = 8 × 104µs = 832µs high (read/write 1)
 * - 0x00 = 00000000 = 832µs low (write 0)
 */

#ifndef _ONEWIRE_UART_H_
#define _ONEWIRE_UART_H_

#include <stdint.h>
#include <stdbool.h>

// UART configuration for OneWire timing
#define ONEWIRE_UART_PORT       2           // UART2
#define ONEWIRE_UART_BAUD       9600        // 104µs per bit
#define ONEWIRE_GPIO_PIN        WB_IO1      // PA9

// OneWire protocol bit patterns for UART transmission
#define ONEWIRE_RESET_PATTERN   0xF0        // Reset pulse pattern
#define ONEWIRE_WRITE_1_PATTERN 0xFF        // Write 1 bit pattern  
#define ONEWIRE_WRITE_0_PATTERN 0x00        // Write 0 bit pattern
#define ONEWIRE_READ_PATTERN    0xFF        // Read bit initiation

// OneWire command bytes
#define ONEWIRE_ROM_SKIP        0xCC
#define ONEWIRE_CONVERT_TEMP    0x44
#define ONEWIRE_READ_SCRATCH    0xBE

// OneWire state machine states
typedef enum {
    OW_STATE_IDLE,
    OW_STATE_RESET,
    OW_STATE_WAIT_PRESENCE,
    OW_STATE_SEND_COMMAND,
    OW_STATE_WAIT_CONVERSION,
    OW_STATE_READ_DATA,
    OW_STATE_COMPLETE,
    OW_STATE_ERROR
} onewire_state_t;

// OneWire operation structure
typedef struct {
    onewire_state_t state;
    uint8_t command_buffer[10];
    uint8_t response_buffer[10];
    uint8_t command_length;
    uint8_t response_length;
    uint8_t current_byte;
    bool device_present;
    int16_t temperature;
} onewire_context_t;

/**
 * @brief Initialize UART-based OneWire interface
 * @return true if successful, false on error
 */
bool onewire_uart_init(void);

/**
 * @brief Start OneWire temperature reading sequence
 * @param context Pointer to OneWire context structure
 * @return true if sequence started, false on error
 */
bool onewire_start_temperature_reading(onewire_context_t* context);

/**
 * @brief OneWire state machine processor (call from timer callback)
 * @param context Pointer to OneWire context structure
 * @return true if operation complete, false if still in progress
 */
bool onewire_process_state_machine(onewire_context_t* context);

/**
 * @brief Send OneWire reset pulse using UART
 * @return true if reset successful (device present), false if no device
 */
bool onewire_uart_reset(void);

/**
 * @brief Send byte via OneWire using UART bit patterns
 * @param byte Byte to send
 * @return true if successful, false on error
 */
bool onewire_uart_write_byte(uint8_t byte);

/**
 * @brief Read byte via OneWire using UART bit patterns
 * @return Received byte, or 0xFF on error
 */
uint8_t onewire_uart_read_byte(void);

/**
 * @brief Convert UART response to OneWire bit
 * @param uart_byte Received UART byte
 * @return true for OneWire bit 1, false for OneWire bit 0
 */
bool onewire_uart_byte_to_bit(uint8_t uart_byte);

/**
 * @brief Check if OneWire operation is complete
 * @param context Pointer to OneWire context structure
 * @return true if complete (success or error), false if in progress
 */
bool onewire_is_complete(onewire_context_t* context);

/**
 * @brief Get temperature result from completed OneWire operation
 * @param context Pointer to OneWire context structure
 * @return Temperature in hundredths of degree C, or INT16_MIN on error
 */
int16_t onewire_get_temperature(onewire_context_t* context);

/**
 * @brief Reset OneWire state machine for new operation
 * @param context Pointer to OneWire context structure
 */
void onewire_reset_state(onewire_context_t* context);

#endif // _ONEWIRE_UART_H_
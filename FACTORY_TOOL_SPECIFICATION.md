# COLDRA Factory Production Tool - Comprehensive Documentation

**Version:** 1.0.0  
**Date:** 2025-09-06  
**Project:** COLDRA Ultra-Low Power Temperature Sensor Manufacturing

## Executive Summary

The COLDRA Factory Production Tool is a comprehensive web-based application designed to automate the manufacturing process of RAK3172-based LoRaWAN temperature sensors. It combines firmware programming, credential provisioning, quality testing, and production tracking into a single streamlined workflow accessible via browser.

## Table of Contents

1. [System Overview](#system-overview)
2. [Hardware Requirements](#hardware-requirements)
3. [Software Architecture](#software-architecture)
4. [Core Features](#core-features)
5. [Database Design](#database-design)
6. [Serial Number System](#serial-number-system)
7. [User Interface Design](#user-interface-design)
8. [Security Considerations](#security-considerations)
9. [Integration Points](#integration-points)
10. [Error Handling](#error-handling)

## System Overview

### Purpose
Automate and track the complete production lifecycle of COLDRA temperature sensors from firmware flashing through credential provisioning to final testing and labeling.

### Key Capabilities
- **Firmware Programming**: Flash region-specific firmware via SWD interface
- **Credential Provisioning**: Program unique LoRaWAN OTAA credentials via UART
- **Quality Testing**: Automated LoRaWAN join test and verification
- **Production Tracking**: Complete database with serial numbers and manufacturing data
- **Device Read-back**: Diagnose and verify returned devices
- **Label Generation**: QR codes and serial number labels for device tracking

### Production Volume
- **Daily Capacity**: 999 devices per region (2,997 total)
- **Annual Capacity**: ~1 million devices
- **Batch Processing**: Sequential, one device at a time
- **Cycle Time**: ~60 seconds per device (programming + provisioning + testing)

## Hardware Requirements

### Manufacturing Fixture
```
┌─────────────────────────────────────┐
│         JST 1.25mm 7-pin            │
│  1:3.3V  2:RX  3:TX  4:SWDIO       │
│  5:SWCLK  6:BOOT  7:GND            │
└────────────┬───────────────────────┘
             │
    ┌────────┴────────┐
    │  Custom PCB     │
    │  Breakout Board │
    └──┬──────────┬──┘
       │          │
   ST-Link V2    FT232
       │          │
    ┌──┴──────────┴──┐
    │  4-Port USB Hub │
    └────────┬────────┘
             │
         [PC USB]
```

### Components
- **Programming Interface**: ST-Link V2/V3 (SWD protocol)
- **Provisioning Interface**: FT232 USB-UART (115200 baud)
- **Connector**: JST SH 1.25mm 7-position
- **USB Hub**: Standard 4-port USB 2.0 hub
- **Power**: 3.3V supplied via ST-Link

### Computer Requirements
- **OS**: Windows 10/11
- **RAM**: 4GB minimum
- **Storage**: 5GB free space  
- **USB**: USB 2.0 or higher
- **Browser**: Chrome, Firefox, or Edge
- **Network**: Local network access

## Software Architecture

### Layer Architecture
```
┌────────────────────────────────────┐
│   Frontend React or Vue.js         │
├────────────────────────────────────┤
│      REST API Layer                │
│   ┌──────────┬──────────────────┐ │
│   │Production│  Device Manager  │ │
│   │Controller│     API          │ │
│   └──────────┴──────────────────┘ │
├────────────────────────────────────┤
│        Service Layer               │
│   ┌──────┬──────┬──────┬──────┐  │
│   │ SWD  │ UART │ TTN  │Label │  │
│   │Service│Service│ API │Print│  │
│   └──────┴──────┴──────┴──────┘  │
├────────────────────────────────────┤
│      Data Access Layer             │
│   ┌──────────┬──────────────────┐ │
│   │  SQLite  │  File System     │ │
│   │    DB    │   (Firmware)     │ │
│   └──────────┴──────────────────┘ │
└────────────────────────────────────┘
```

### Revised Technology Stack (Local Browser-Based)
- **Architecture**: Local Client-Server (Node.js Backend + Web UI)
- **Backend Framework**: Node.js with Express.js
- **Frontend Framework**: React or Vue.js
- **Database**: SQLite 3.x
- **API & Real-time**: REST API & WebSockets
- **Serial Communication**: node-serialport Library
- **External Tool Runner**: Node.js child_process module
- **UI Pattern**: Component-Based State Management
- **Application Packaging**: Electron or Tauri

### Module Breakdown

#### 1. Firmware Programming Module
- **Interface**: `IFirmwareProgrammer`
- **Implementation**: `STM32CubeProgrammer`
- **Functions**:
  - `ProgramFirmware(string hexFile, string devicePort)`
  - `VerifyFirmware(string hexFile, string devicePort)`
  - `EraseFirmware(string devicePort)`
  - `ReadDeviceInfo(string devicePort)`

#### 2. Credential Provisioning Module
- **Interface**: `ICredentialProvisioner`
- **Implementation**: `UARTProvisioner`
- **Functions**:
  - `ProvisionOTAA(string port, OTAACredentials creds)`
  - `ReadCredentials(string port)`
  - `TestJoin(string port, int timeout)`
  - `GetDeviceEUI(string port)`

#### 3. Serial Number Generator
- **Interface**: `ISerialNumberGenerator`
- **Implementation**: `COLDRASerialGenerator`
- **Functions**:
  - `GenerateSerial(Region region, DateTime date)`
  - `ParseSerial(string serialNumber)`
  - `ValidateSerial(string serialNumber)`
  - `GetNextCounter(Region region, DateTime date)`

#### 4. Database Manager
- **Interface**: `IDatabaseManager`
- **Implementation**: `SQLiteManager`
- **Functions**:
  - `RecordDevice(DeviceRecord record)`
  - `FindDevice(string serialNumber)`
  - `SearchDevices(SearchCriteria criteria)`
  - `ExportToCSV(string filePath, List<DeviceRecord>)`

#### 5. Label Printer Module
- **Interface**: `ILabelPrinter`
- **Implementation**: `QRLabelPrinter`
- **Functions**:
  - `GenerateQRCode(DeviceInfo info)`
  - `PrintLabel(LabelData data)`
  - `PreviewLabel(LabelData data)`
  - `ExportLabelPDF(string filePath, LabelData data)`

## Core Features

### 1. Production Workflow

#### Step 1: Initialization
```csharp
workflow.Initialize() {
    1. Load regional firmware binaries
    2. Connect to database
    3. Verify ST-Link presence
    4. Verify UART availability
    5. Load TTN credentials
    6. Initialize serial counter
}
```

#### Step 2: Device Programming
```csharp
workflow.ProgramDevice() {
    1. Detect device on SWD
    2. Read chip ID/DevEUI
    3. Select firmware by region
    4. Erase flash memory
    5. Program firmware
    6. Verify programming
    7. Log to database
}
```

#### Step 3: Credential Provisioning
```csharp
workflow.ProvisionCredentials() {
    1. Open UART connection
    2. Send AT+APPEUI command
    3. Send AT+APPKEY command
    4. Configure regional settings
    5. Save to flash (AT&W)
    6. Verify credentials
    7. Update database
}
```

#### Step 4: Quality Testing
```csharp
workflow.TestDevice() {
    1. Initiate LoRaWAN join
    2. Monitor join status (60s timeout)
    3. Read battery voltage
    4. Verify sensor communication
    5. Record test results
    6. Mark pass/fail status
}
```

#### Step 5: Labeling
```csharp
workflow.GenerateLabel() {
    1. Create serial number
    2. Generate QR code
    3. Format label data
    4. Print/save label
    5. Update database
}
```

### 2. Read-Back Functionality

For devices returned from field:
```csharp
readback.AnalyzeDevice() {
    1. Read DevEUI via UART
    2. Query database for history
    3. Read current credentials
    4. Check firmware version
    5. Read operational counters
    6. Test sensor functionality
    7. Generate diagnostic report
}
```

### 3. Batch Operations

Production batch management:
```csharp
batch.ProcessBatch() {
    foreach device in batch {
        1. Program firmware
        2. Provision credentials
        3. Test device
        4. Generate label
        5. Update statistics
        6. Handle errors
    }
    7. Generate batch report
    8. Export to CSV
}
```

## Database Design

### Schema Definition

```sql
-- Main device tracking table
CREATE TABLE devices (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    serial_number TEXT UNIQUE NOT NULL,
    deveui TEXT UNIQUE NOT NULL,
    appeui TEXT NOT NULL,
    appkey_encrypted TEXT NOT NULL,
    region TEXT NOT NULL,
    firmware_version TEXT NOT NULL,
    hardware_version TEXT,
    manufacture_date DATETIME NOT NULL,
    manufacture_time_seconds INTEGER,
    operator_id TEXT NOT NULL,
    station_id TEXT,
    
    -- Status tracking
    programming_status TEXT NOT NULL,
    programming_timestamp DATETIME,
    provisioning_status TEXT NOT NULL,
    provisioning_timestamp DATETIME,
    test_status TEXT,
    test_timestamp DATETIME,
    
    -- Test results
    join_test_result BOOLEAN,
    join_time_ms INTEGER,
    battery_voltage_mv INTEGER,
    sensor_test_result BOOLEAN,
    
    -- Additional metadata
    batch_number TEXT,
    customer_code TEXT,
    notes TEXT,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- Audit log table
CREATE TABLE audit_log (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    device_id INTEGER,
    action TEXT NOT NULL,
    operator_id TEXT NOT NULL,
    timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
    details TEXT,
    result TEXT,
    error_message TEXT,
    FOREIGN KEY (device_id) REFERENCES devices(id)
);

-- TTN credentials storage
CREATE TABLE ttn_applications (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    app_name TEXT UNIQUE NOT NULL,
    app_id TEXT NOT NULL,
    app_eui TEXT NOT NULL,
    api_key_encrypted TEXT,
    region TEXT NOT NULL,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- Serial number tracking
CREATE TABLE serial_counters (
    date DATE NOT NULL,
    region TEXT NOT NULL,
    counter INTEGER NOT NULL,
    PRIMARY KEY (date, region)
);

-- Indexes for performance
CREATE INDEX idx_serial_number ON devices(serial_number);
CREATE INDEX idx_deveui ON devices(deveui);
CREATE INDEX idx_manufacture_date ON devices(manufacture_date);
CREATE INDEX idx_region ON devices(region);
```

### Data Retention
- **Production Data**: Permanent retention
- **Audit Logs**: 2 years minimum
- **Test Results**: Permanent retention
- **Backup Schedule**: Daily automated backups

## Serial Number System

### Format: DDMMYY-R-NNN

#### Components
- **DDMMYY**: Manufacturing date (06-Dec-2025 = 061225)
- **R**: Region code
  - U = US915 (United States)
  - E = EU868 (Europe)
  - I = IN865 (India)
- **NNN**: Daily counter (001-999)

#### Examples
- `061225-U-001`: First US device on Dec 6, 2025
- `061225-E-047`: 47th EU device on Dec 6, 2025
- `071225-I-999`: 999th India device on Dec 7, 2025

#### Capacity Analysis
- **Per Region**: 999 devices/day
- **Total Daily**: 2,997 devices (all regions)
- **Annual**: ~1,094,000 devices/year
- **Uniqueness Period**: 100 years (YY format)

#### Collision Prevention
- Database enforces unique constraint
- Real-time counter synchronization
- Multi-site manufacturing requires site code extension

### QR Code Content
```json
{
  "sn": "061225-U-001",
  "deveui": "AC1F09FFFE123456",
  "fw": "2.2.0",
  "mfg": "2025-12-06"
}
```

## User Interface Design

### Main Window Layout

```
┌─────────────────────────────────────────────────────┐
│ COLDRA Factory Tool v1.0.0        [─] [□] [X]      │
├─────────────────────────────────────────────────────┤
│ [Production] [Read-Back] [Database] [Settings]      │
├─────────────────────────────────────────────────────┤
│                                                      │
│  ┌─────────────────┐  ┌─────────────────────────┐  │
│  │ Device Info     │  │ Progress                │  │
│  │                 │  │                         │  │
│  │ Region: [US▼]   │  │ [████████████░░░] 75%  │  │
│  │ Serial: AUTO    │  │                         │  │
│  │ DevEUI: ---     │  │ Status: Programming...  │  │
│  │                 │  │                         │  │
│  └─────────────────┘  └─────────────────────────┘  │
│                                                      │
│  ┌──────────────────────────────────────────────┐  │
│  │ Workflow Steps                               │  │
│  │                                              │  │
│  │ [✓] 1. Connect Device                        │  │
│  │ [✓] 2. Program Firmware                      │  │
│  │ [●] 3. Provision Credentials                 │  │
│  │ [ ] 4. Test LoRaWAN Join                     │  │
│  │ [ ] 5. Generate Label                        │  │
│  │                                              │  │
│  └──────────────────────────────────────────────┘  │
│                                                      │
│  ┌──────────────────────────────────────────────┐  │
│  │ Log Output                                   │  │
│  │ 14:23:45 - Device detected: STM32WLE5       │  │
│  │ 14:23:46 - Programming started...            │  │
│  │ 14:23:52 - Programming complete              │  │
│  │ 14:23:53 - Provisioning OTAA credentials... │  │
│  └──────────────────────────────────────────────┘  │
│                                                      │
│  [Start Production] [Pause] [Cancel] [Export Log]   │
└─────────────────────────────────────────────────────┤
│ Devices Today: 47 | Total: 1,234 | Yield: 98.2%   │
└─────────────────────────────────────────────────────┘
```

### Settings Tab

```
TTN Credentials Management
├── Application Name: [________________]
├── AppEUI: [________________]
├── AppKey: [________________]
└── [Add] [Update] [Delete]

Regional Configuration
├── US915: [✓] Sub-band: [2▼] TX Power: [14dBm▼]
├── EU868: [✓] Channels: [0-7] TX Power: [14dBm▼]
└── IN865: [✓] Channels: [0-2] TX Power: [20dBm▼]

Operator Management
├── Current Operator: [John Doe▼]
├── Station ID: [PROD-01]
└── [Manage Operators...]

Firmware Management
├── US915: v2.2.0 [Browse...] [✓]
├── EU868: v2.2.0 [Browse...] [✓]
└── IN865: v2.2.0 [Browse...] [✓]
```

### Database Tab

```
Search Filters
├── Date Range: [2025-12-01] to [2025-12-06]
├── Region: [All▼]
├── Status: [All▼]
├── Serial: [_______________]
└── DevEUI: [_______________]

[Search] [Clear] [Export CSV] [Print Report]

Results Grid:
┌────────────┬──────────────────┬─────────┬────────┐
│Serial      │DevEUI            │Region   │Status  │
├────────────┼──────────────────┼─────────┼────────┤
│061225-U-001│AC1F09FFFE123456 │US915    │Pass    │
│061225-U-002│AC1F09FFFE123457 │US915    │Pass    │
│061225-E-001│AC1F09FFFE123458 │EU868    │Fail    │
└────────────┴──────────────────┴─────────┴────────┘
```

## Security Considerations

### Credential Protection
1. **AppKey Encryption**
   - AES-256 encryption in database
   - Windows DPAPI for key storage
   - Never logged in plaintext

2. **Access Control**
   
   - Audit trail for all operations

3. **Data Transmission**
   - HTTPS for TTN API calls
   - Encrypted database backups
   - Secure credential input (masked fields)

### Security Best Practices
```csharp
// AppKey handling
class CredentialManager {
    private SecureString StoreAppKey(string appKey) {
        // Convert to SecureString
        // Encrypt with DPAPI
        // Store encrypted value
    }
    
    private string RetrieveAppKey(SecureString encrypted) {
        // Decrypt with DPAPI
        // Use in memory briefly
        // Clear after use
    }
}
```

### Compliance Requirements
- **Data Protection**: GDPR compliance for EU devices
- **Audit Trail**: Complete operation history
- **Traceability**: Full device lifecycle tracking
- **Export Control**: Firmware distribution tracking

## Integration Points

### 1. STM32CubeProgrammer CLI
```batch
# Programming command
STM32_Programmer_CLI.exe -c port=SWD -w firmware.hex 0x08000000 -v

# Verification command
STM32_Programmer_CLI.exe -c port=SWD -v firmware.hex

# Read device info
STM32_Programmer_CLI.exe -c port=SWD -r32 0x1FFF7590 8
```

### 2. UART AT Commands
```python
# From provision_credentials.py
serial.write("AT+APPEUI=1234567890ABCDEF\r\n")
serial.write("AT+APPKEY=00112233445566778899AABBCCDDEEFF\r\n")
serial.write("AT&W\r\n")  # Save to flash
```

### 3. TTN API Integration
```csharp
// Device registration
POST https://eu1.cloud.thethings.network/api/v3/applications/{app_id}/devices
{
    "end_device": {
        "ids": {
            "device_id": "coldra-061225-u-001",
            "dev_eui": "AC1F09FFFE123456",
            "join_eui": "1234567890ABCDEF"
        },
        "join_server_address": "eu1.cloud.thethings.network",
        "network_server_address": "eu1.cloud.thethings.network",
        "application_server_address": "eu1.cloud.thethings.network"
    }
}
```

### 4. Label Printer API
```csharp
// ZPL for Zebra printers
string zpl = @"
^XA
^FO50,50^BQN,2,10
^FDMM,A061225-U-001^FS
^FO50,350^A0N,30,30^FDSerial: 061225-U-001^FS
^FO50,400^A0N,30,30^FDDevEUI: AC1F09FFFE123456^FS
^XZ";
```

## Error Handling

### Error Categories

#### 1. Hardware Errors
- **E1001**: ST-Link not detected
- **E1002**: Device not responding on SWD
- **E1003**: UART port not available
- **E1004**: Programming verification failed

#### 2. Provisioning Errors
- **E2001**: Invalid AppEUI format
- **E2002**: Invalid AppKey format
- **E2003**: AT command timeout
- **E2004**: Credentials save failed

#### 3. Test Errors
- **E3001**: LoRaWAN join timeout
- **E3002**: Join rejected by network
- **E3003**: Sensor communication failed
- **E3004**: Battery voltage out of range

#### 4. Database Errors
- **E4001**: Database connection failed
- **E4002**: Duplicate serial number
- **E4003**: Transaction rollback
- **E4004**: Export failed

### Error Recovery Strategies

```csharp
class ErrorHandler {
    public async Task<bool> HandleError(ErrorCode code, Device device) {
        switch(code) {
            case ErrorCode.ProgrammingFailed:
                // Retry up to 3 times
                // Erase and reprogram
                // Mark device for manual inspection
                break;
                
            case ErrorCode.JoinTimeout:
                // Retry with different sub-band
                // Check credentials
                // Verify antenna connection
                break;
                
            case ErrorCode.DatabaseError:
                // Cache locally
                // Retry when connection restored
                // Alert operator
                break;
        }
    }
}
```

### Operator Notifications
- **Visual**: Red/yellow/green status indicators
- **Audio**: Beep patterns for errors
- **Log**: Detailed error messages with timestamps
- **Action**: Clear recovery instructions

## Performance Specifications

### Timing Requirements
- **Firmware Programming**: 15-20 seconds
- **Credential Provisioning**: 5-10 seconds
- **Join Test**: 30-60 seconds
- **Label Generation**: 2-3 seconds
- **Total Cycle Time**: ~60 seconds per device

### Throughput Targets
- **Hourly**: 60 devices (optimal conditions)
- **Daily**: 480 devices (8-hour shift)
- **Yield Target**: >95% first-pass success

### Resource Usage
- **CPU**: <25% average utilization
- **RAM**: <500MB application footprint
- **Disk**: <10GB total (including database)
- **Network**: Minimal (only for TTN API)

## Testing Strategy

### Unit Testing
- Serial number generation algorithms
- Database CRUD operations
- Credential encryption/decryption
- Parser for AT command responses

### Integration Testing
- STM32CubeProgrammer CLI integration
- UART communication reliability
- TTN API interactions
- Database transaction integrity

### System Testing
- Full production workflow
- Error recovery scenarios
- Performance under load
- Multi-operator concurrency

### Acceptance Testing
- Manufacturing operator training
- Production line integration
- Quality metrics validation
- Regulatory compliance verification

## Maintenance and Support

### Regular Maintenance
- **Daily**: Database backup
- **Weekly**: Log rotation
- **Monthly**: Performance reports
- **Quarterly**: Firmware updates

### Troubleshooting Guide
1. **Device Not Detected**
   - Check USB connections
   - Verify ST-Link drivers
   - Confirm device power

2. **Programming Failures**
   - Verify firmware file integrity
   - Check flash memory capacity
   - Ensure proper voltage levels

3. **Join Test Failures**
   - Verify TTN gateway proximity
   - Check credential accuracy
   - Confirm regional settings

### Support Contacts
- **Technical Support**: Internal IT team
- **RAK Support**: <EMAIL>
- **TTN Community**: forum.thethingsnetwork.org

## Appendix A: Regional Configurations

### US915 (United States)
- **Frequency**: 902-928 MHz
- **Channels**: 64 + 8 (500kHz)
- **Sub-bands**: 8 (TTN uses sub-band 2)
- **TX Power**: +20dBm max (typically +14dBm)
- **Data Rates**: DR0-DR4 (SF10-SF7)

### EU868 (Europe)
- **Frequency**: 863-870 MHz
- **Channels**: 3 default + 5 optional
- **Duty Cycle**: 1% or 0.1%
- **TX Power**: +14dBm max
- **Data Rates**: DR0-DR7 (SF12-SF7)

### IN865 (India)
- **Frequency**: 865-867 MHz
- **Channels**: 3 default
- **TX Power**: +20dBm max
- **Data Rates**: DR0-DR7 (SF12-SF7)
- **Specific Requirements**: WPC compliance

## Appendix B: AT Command Reference

### Essential Commands
```
AT                      - Test command
AT+DEVEUI=?            - Get DevEUI
AT+APPEUI=<value>      - Set AppEUI
AT+APPKEY=<value>      - Set AppKey
AT+NWM=1               - Set to LoRaWAN mode
AT+NJM=1               - Set to OTAA
AT+CLASS=A             - Set Class A
AT+BAND=5              - Set US915
AT+MASK=0002           - Set sub-band 2
AT+JOIN                - Start join procedure
AT&W                   - Save configuration
AT+RESET               - Reset device
```

## Appendix C: Database Queries

### Common Queries
```sql
-- Daily production report
SELECT region, COUNT(*) as count, 
       SUM(CASE WHEN test_status='PASS' THEN 1 ELSE 0 END) as passed
FROM devices
WHERE DATE(manufacture_date) = DATE('now')
GROUP BY region;

-- Device lookup
SELECT * FROM devices 
WHERE serial_number = ? OR deveui = ?;

-- Yield calculation
SELECT 
    COUNT(*) as total,
    SUM(CASE WHEN test_status='PASS' THEN 1 ELSE 0 END) as passed,
    (SUM(CASE WHEN test_status='PASS' THEN 1.0 ELSE 0 END) / COUNT(*)) * 100 as yield_percent
FROM devices
WHERE manufacture_date BETWEEN ? AND ?;
```

## Version History

### v1.0.0 (2025-09-06)
- Initial specification
- Core functionality defined
- Database schema created
- UI mockups completed

---

**Document Status:** FINAL  
**Review Status:** Pending  
**Approval Status:** Pending
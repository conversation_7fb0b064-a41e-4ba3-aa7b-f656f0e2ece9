/**
 * COLDRA Factory Tool - API Routes
 * Version: 1.0.0
 * 
 * Main API routes for the COLDRA Factory Tool
 */

const express = require('express');
const ConfigManager = require('../config/configManager');
const logger = require('../utils/logger');
const VersionManager = require('../utils/versionManager');
const dbConnection = require('../database/connection');
const DatabaseSchema = require('../database/schema');
const SerialNumberGenerator = require('../utils/serialNumberGenerator');
const DeviceManager = require('../database/deviceManager');
const ValidationMiddleware = require('../middleware/validation');
const DatabaseMigrations = require('../database/migrations');
const router = express.Router();

// Apply security middleware to all routes
router.use(ValidationMiddleware.securityCheck);

// Initialize managers
const configManager = new ConfigManager();
const versionManager = new VersionManager();

// Database schema will be initialized when needed
let dbSchema = null;
let serialGenerator = null;
let deviceManager = null;
let databaseMigrations = null;

function getDbSchema() {
    if (!dbSchema) {
        dbSchema = new DatabaseSchema(dbConnection);
    }
    return dbSchema;
}

function getSerialGenerator() {
    if (!serialGenerator) {
        serialGenerator = new SerialNumberGenerator(dbConnection);
    }
    return serialGenerator;
}

function getDeviceManager() {
    if (!deviceManager) {
        deviceManager = new DeviceManager(dbConnection);
    }
    return deviceManager;
}

function getDatabaseMigrations() {
    if (!databaseMigrations) {
        databaseMigrations = new DatabaseMigrations(dbConnection);
    }
    return databaseMigrations;
}

// Middleware for API routes
router.use((req, res, next) => {
    // Add API-specific headers
    res.setHeader('X-API-Version', '1.0.0');
    res.setHeader('X-Service', 'COLDRA-Factory-Tool');
    next();
});

// Health check endpoint
router.get('/health', (req, res) => {
    res.json({
        status: 'OK',
        service: 'COLDRA Factory Tool',
        version: '1.0.0',
        timestamp: new Date().toISOString(),
        uptime: process.uptime(),
        environment: process.env.NODE_ENV || 'development',
        node_version: process.version,
        memory_usage: process.memoryUsage(),
        branding: {
            colors: {
                dark_teal: '#006E74',
                light_teal: '#0097AC',
                white: '#FFFFFF',
                soft_black: '#231F20'
            }
        }
    });
});

// System status endpoint
router.get('/status', (req, res) => {
    res.json({
        system: 'COLDRA Factory Tool',
        status: 'running',
        version: '1.0.0',
        components: {
            server: 'online',
            database: 'not_configured',
            hardware: 'not_detected',
            firmware: 'not_loaded'
        },
        statistics: {
            devices_today: 0,
            total_devices: 0,
            yield_percentage: 0
        },
        last_updated: new Date().toISOString()
    });
});

// Version information endpoint (enhanced)
router.get('/version', (req, res) => {
    try {
        const versionInfo = versionManager.getVersionInfo();
        logger.info('Version information requested', { endpoint: '/api/version' });

        res.json({
            application: 'COLDRA Factory Tool',
            ...versionInfo,
            swe_compliant: true
        });
    } catch (error) {
        logger.error('Version endpoint error', error);
        res.status(500).json({
            error: 'Unable to retrieve version information',
            timestamp: new Date().toISOString()
        });
    }
});

// SWE System Manifest endpoint
router.get('/version/manifest', (req, res) => {
    try {
        const manifest = versionManager.getSystemManifest();
        logger.info('System manifest requested', { endpoint: '/api/version/manifest' });

        res.json({
            system: 'COLDRA Factory Tool',
            manifest: manifest,
            swe_compliance: 'ENABLED'
        });
    } catch (error) {
        logger.error('Manifest endpoint error', error);
        res.status(500).json({
            error: 'Unable to generate system manifest',
            timestamp: new Date().toISOString()
        });
    }
});

// Configuration endpoints
router.get('/config/test', (req, res) => {
    try {
        // Test loading sample TTN config
        const result = configManager.loadTTNCredentials('./config/sample_ttn_credentials.json');

        if (result.success) {
            res.json({
                message: 'Configuration system working',
                config_loaded: true,
                config_data: result.config,
                timestamp: new Date().toISOString()
            });
        } else {
            res.status(400).json({
                message: 'Configuration validation failed',
                error: result.error,
                timestamp: new Date().toISOString()
            });
        }
    } catch (error) {
        res.status(500).json({
            message: 'Configuration system error',
            error: error.message,
            timestamp: new Date().toISOString()
        });
    }
});

// Logging test endpoint
router.get('/logs/test', (req, res) => {
    try {
        // Test different log levels
        logger.info('Testing logging system', { endpoint: '/api/logs/test' });
        logger.warn('This is a warning message', { test: true });
        logger.production.deviceStart('TEST-001', 'TestOperator');
        logger.production.hardwareEvent('USB_CONNECTED', { device: 'ST-Link' });

        res.json({
            message: 'Logging system test completed',
            logs_generated: 4,
            log_levels: ['info', 'warn', 'production'],
            timestamp: new Date().toISOString()
        });
    } catch (error) {
        logger.error('Logging test failed', error);
        res.status(500).json({
            message: 'Logging test failed',
            error: error.message,
            timestamp: new Date().toISOString()
        });
    }
});

// Database connection test endpoint
router.get('/database/test', async (req, res) => {
    try {
        const testResult = await dbConnection.testConnection();
        const stats = dbConnection.getStats();

        logger.info('Database test requested', { endpoint: '/api/database/test' });

        res.json({
            message: 'Database connection test completed',
            connection: testResult,
            statistics: stats,
            timestamp: new Date().toISOString()
        });
    } catch (error) {
        logger.error('Database test failed', error);
        res.status(500).json({
            message: 'Database test failed',
            error: error.message,
            timestamp: new Date().toISOString()
        });
    }
});

// Database schema endpoints
router.get('/database/schema/check', async (req, res) => {
    try {
        const schemaInfo = await getDbSchema().checkSchema();
        logger.info('Database schema check requested', { endpoint: '/api/database/schema/check' });

        // Check if request wants HTML format
        if (req.headers.accept && req.headers.accept.includes('text/html')) {
            let html = `
                <html>
                <head>
                    <title>Database Schema Check</title>
                    <style>
                        body { font-family: Arial, sans-serif; background: #006E74; color: white; padding: 20px; }
                        .container { max-width: 800px; margin: 0 auto; background: rgba(0,151,172,0.1); padding: 20px; border-radius: 10px; }
                        h1 { color: #0097AC; }
                        .success { color: #00ff00; }
                        .table { background: #0097AC; padding: 10px; margin: 5px 0; border-radius: 5px; }
                        .stats { background: rgba(255,255,255,0.1); padding: 15px; border-radius: 5px; margin: 20px 0; }
                        .back { color: #0097AC; text-decoration: none; }
                    </style>
                </head>
                <body>
                    <div class="container">
                        <h1>🗄️ Database Schema Status</h1>`;

            if (schemaInfo.exists) {
                html += `
                        <p class="success">✅ Database schema exists and is ready!</p>
                        <div class="stats">
                            <h3>📊 Schema Information:</h3>
                            <p><strong>Total Tables:</strong> ${schemaInfo.table_count}</p>
                            <h4>Tables Created:</h4>`;

                schemaInfo.tables.forEach(table => {
                    html += `<div class="table">📋 ${table}</div>`;
                });
            } else {
                html += `
                        <p style="color: #ffaa00;">⚠️ Database schema not found!</p>
                        <p>Use POST /api/database/schema/create to create the schema.</p>`;
            }

            html += `
                        </div>
                        <p><a href="/" class="back">← Back to Main Page</a></p>
                    </div>
                </body>
                </html>`;

            res.send(html);
        } else {
            res.json({
                message: 'Database schema check completed',
                schema: schemaInfo,
                timestamp: new Date().toISOString()
            });
        }
    } catch (error) {
        logger.error('Database schema check failed', error);
        res.status(500).json({
            message: 'Database schema check failed',
            error: error.message,
            timestamp: new Date().toISOString()
        });
    }
});

router.post('/database/schema/create', async (req, res) => {
    try {
        const schema = getDbSchema();
        const result = await schema.createTables();
        await schema.insertInitialConfig();

        logger.info('Database schema creation requested', { endpoint: '/api/database/schema/create' });

        res.json({
            message: 'Database schema created successfully',
            result: result,
            timestamp: new Date().toISOString()
        });
    } catch (error) {
        logger.error('Database schema creation failed', error);
        res.status(500).json({
            message: 'Database schema creation failed',
            error: error.message,
            timestamp: new Date().toISOString()
        });
    }
});

// Serial number generation endpoints
router.post('/serial/generate', ValidationMiddleware.validateSerialGeneration, async (req, res) => {
    try {
        const { region } = req.body;
        const generator = getSerialGenerator();
        const result = await generator.generateSerialNumber(region);

        logger.info('Serial number generation requested', {
            endpoint: '/api/serial/generate',
            region: region || 'US915'
        });

        res.json({
            message: 'Serial number generated successfully',
            serial_data: result,
            timestamp: new Date().toISOString()
        });
    } catch (error) {
        logger.error('Serial number generation failed', error);
        res.status(500).json({
            message: 'Serial number generation failed',
            error: error.message,
            timestamp: new Date().toISOString()
        });
    }
});

router.get('/serial/test', async (req, res) => {
    try {
        const generator = getSerialGenerator();
        const testResults = [];

        // Generate test serial numbers for all regions
        for (const region of ['US915', 'EU868', 'IN865']) {
            const result = await generator.generateSerialNumber(region);
            testResults.push(result);
        }

        const stats = await generator.getStatistics();

        logger.info('Serial number test requested', { endpoint: '/api/serial/test' });

        // Check if request wants HTML format
        if (req.headers.accept && req.headers.accept.includes('text/html')) {
            let html = `
                <html>
                <head>
                    <title>Serial Number Generator Test</title>
                    <style>
                        body { font-family: Arial, sans-serif; background: #006E74; color: white; padding: 20px; }
                        .container { max-width: 800px; margin: 0 auto; background: rgba(0,151,172,0.1); padding: 20px; border-radius: 10px; }
                        h1 { color: #0097AC; }
                        .serial { background: #0097AC; padding: 10px; margin: 10px 0; border-radius: 5px; font-size: 18px; font-weight: bold; }
                        .stats { background: rgba(255,255,255,0.1); padding: 15px; border-radius: 5px; margin: 20px 0; }
                        .success { color: #00ff00; }
                        .back { color: #0097AC; text-decoration: none; }
                    </style>
                </head>
                <body>
                    <div class="container">
                        <h1>🔢 Serial Number Generator Test Results</h1>
                        <p class="success">✅ Test completed successfully!</p>

                        <h2>Generated Serial Numbers:</h2>`;

            testResults.forEach(result => {
                html += `
                        <div class="serial">
                            📱 ${result.serial_number}
                            <small>(${result.region} - Counter: ${result.counter})</small>
                        </div>`;
            });

            html += `
                        <div class="stats">
                            <h3>📊 Statistics:</h3>
                            <p><strong>Total Generated Today:</strong> ${stats.total_generated}</p>
                            <p><strong>By Region:</strong></p>
                            <ul>`;

            Object.entries(stats.by_region).forEach(([region, count]) => {
                html += `<li>${region}: ${count} devices</li>`;
            });

            html += `
                            </ul>
                        </div>

                        <p><a href="/" class="back">← Back to Main Page</a></p>
                    </div>
                </body>
                </html>`;

            res.send(html);
        } else {
            res.json({
                message: 'Serial number generator test completed',
                test_results: testResults,
                statistics: stats,
                timestamp: new Date().toISOString()
            });
        }
    } catch (error) {
        logger.error('Serial number test failed', error);
        res.status(500).json({
            message: 'Serial number test failed',
            error: error.message,
            timestamp: new Date().toISOString()
        });
    }
});

router.post('/serial/validate', async (req, res) => {
    try {
        const { serial_number } = req.body;

        if (!serial_number) {
            return res.status(400).json({
                message: 'Serial number is required',
                timestamp: new Date().toISOString()
            });
        }

        const generator = getSerialGenerator();
        const validation = generator.validateSerialNumber(serial_number);
        const exists = validation.valid ? await generator.checkSerialNumberExists(serial_number) : false;

        logger.info('Serial number validation requested', {
            endpoint: '/api/serial/validate',
            serial_number: serial_number
        });

        res.json({
            message: 'Serial number validation completed',
            validation: validation,
            exists_in_database: exists,
            timestamp: new Date().toISOString()
        });
    } catch (error) {
        logger.error('Serial number validation failed', error);
        res.status(500).json({
            message: 'Serial number validation failed',
            error: error.message,
            timestamp: new Date().toISOString()
        });
    }
});

// Device CRUD endpoints
router.post('/devices/create', ValidationMiddleware.validateDeviceData, async (req, res) => {
    try {
        const deviceManager = getDeviceManager();
        const result = await deviceManager.createDevice(req.body);

        logger.info('Device creation requested', {
            endpoint: '/api/devices/create',
            serial_number: req.body.serial_number
        });

        res.json({
            message: 'Device created successfully',
            device: result,
            timestamp: new Date().toISOString()
        });
    } catch (error) {
        logger.error('Device creation failed', error);
        res.status(500).json({
            message: 'Device creation failed',
            error: error.message,
            timestamp: new Date().toISOString()
        });
    }
});

// FIXED: Move specific routes BEFORE parameterized routes to avoid conflicts
router.get('/devices/search', async (req, res) => {
    try {
        const { serial_number, region, status, operator_id, date_from, date_to } = req.query;
        const deviceManager = getDeviceManager();

        const searchCriteria = {
            serial_number,
            region,
            status,
            operator_id,
            date_from,
            date_to
        };

        const devices = await deviceManager.searchDevices(searchCriteria);

        logger.info('Device search requested', {
            endpoint: '/api/devices/search',
            criteria: searchCriteria
        });

        res.json({
            message: 'Device search completed',
            devices: devices,
            count: devices.length,
            criteria: searchCriteria,
            timestamp: new Date().toISOString()
        });
    } catch (error) {
        logger.error('Device search failed', error);
        res.status(500).json({
            message: 'Device search failed',
            error: error.message,
            timestamp: new Date().toISOString()
        });
    }
});

router.get('/devices/stats', async (req, res) => {
    try {
        const deviceMgr = getDeviceManager();
        const stats = await deviceMgr.getDeviceStatistics();

        logger.info('Device statistics requested', { endpoint: '/api/devices/stats' });

        res.json({
            message: 'Device statistics retrieved',
            statistics: stats,
            timestamp: new Date().toISOString()
        });
    } catch (error) {
        logger.error('Device statistics failed', error);
        res.status(500).json({
            message: 'Device statistics failed',
            error: error.message,
            timestamp: new Date().toISOString()
        });
    }
});

router.get('/devices/test', async (req, res) => {
    try {
        const deviceMgr = getDeviceManager();

        logger.info('Device lookup requested', { endpoint: '/api/devices/test' });

        res.json({
            message: 'Device manager test endpoint working',
            status: 'success',
            timestamp: new Date().toISOString()
        });
    } catch (error) {
        logger.error('Device test failed', error);
        res.status(500).json({
            message: 'Device test failed',
            error: error.message,
            timestamp: new Date().toISOString()
        });
    }
});

// FIXED: Add parameterized routes AFTER specific routes
router.get('/devices/:id', async (req, res) => {
    try {
        const { id } = req.params;
        const deviceManager = getDeviceManager();
        const device = await deviceManager.getDeviceById(id);

        if (!device) {
            return res.status(404).json({
                message: 'Device not found',
                device_id: id,
                timestamp: new Date().toISOString()
            });
        }

        logger.info('Device lookup requested', {
            endpoint: '/api/devices/:id',
            device_id: id
        });

        res.json({
            message: 'Device retrieved successfully',
            device: device,
            timestamp: new Date().toISOString()
        });
    } catch (error) {
        logger.error('Device lookup failed', error);
        res.status(500).json({
            message: 'Device lookup failed',
            error: error.message,
            timestamp: new Date().toISOString()
        });
    }
});

router.put('/devices/:id', ValidationMiddleware.validateDeviceData, async (req, res) => {
    try {
        const { id } = req.params;
        const deviceManager = getDeviceManager();
        const result = await deviceManager.updateDevice(id, req.body);

        logger.info('Device update requested', {
            endpoint: '/api/devices/:id',
            device_id: id
        });

        res.json({
            message: 'Device updated successfully',
            device: result,
            timestamp: new Date().toISOString()
        });
    } catch (error) {
        logger.error('Device update failed', error);
        res.status(500).json({
            message: 'Device update failed',
            error: error.message,
            timestamp: new Date().toISOString()
        });
    }
});

router.delete('/devices/:id', async (req, res) => {
    try {
        const { id } = req.params;
        const deviceManager = getDeviceManager();
        const result = await deviceManager.deleteDevice(id);

        logger.info('Device deletion requested', {
            endpoint: '/api/devices/:id',
            device_id: id
        });

        res.json({
            message: 'Device deleted successfully',
            result: result,
            timestamp: new Date().toISOString()
        });
    } catch (error) {
        logger.error('Device deletion failed', error);
        res.status(500).json({
            message: 'Device deletion failed',
            error: error.message,
            timestamp: new Date().toISOString()
        });
    }
});

// Production workflow endpoints
router.post('/devices/:id/program', async (req, res) => {
    try {
        const { id } = req.params;
        const { firmware_version, operator_id } = req.body;
        const deviceManager = getDeviceManager();

        const result = await deviceManager.updateDeviceStatus(id, 'programming_status', 'IN_PROGRESS', operator_id);

        logger.info('Device programming started', {
            endpoint: '/api/devices/:id/program',
            device_id: id,
            firmware_version,
            operator_id
        });

        res.json({
            message: 'Device programming started',
            device_id: id,
            status: 'IN_PROGRESS',
            firmware_version,
            operator_id,
            timestamp: new Date().toISOString()
        });
    } catch (error) {
        logger.error('Device programming start failed', error);
        res.status(500).json({
            message: 'Device programming start failed',
            error: error.message,
            timestamp: new Date().toISOString()
        });
    }
});

router.post('/devices/:id/provision', async (req, res) => {
    try {
        const { id } = req.params;
        const { operator_id } = req.body;
        const deviceManager = getDeviceManager();

        const result = await deviceManager.updateDeviceStatus(id, 'provisioning_status', 'IN_PROGRESS', operator_id);

        logger.info('Device provisioning started', {
            endpoint: '/api/devices/:id/provision',
            device_id: id,
            operator_id
        });

        res.json({
            message: 'Device provisioning started',
            device_id: id,
            status: 'IN_PROGRESS',
            operator_id,
            timestamp: new Date().toISOString()
        });
    } catch (error) {
        logger.error('Device provisioning start failed', error);
        res.status(500).json({
            message: 'Device provisioning start failed',
            error: error.message,
            timestamp: new Date().toISOString()
        });
    }
});

router.post('/devices/:id/test', async (req, res) => {
    try {
        const { id } = req.params;
        const { operator_id, test_results } = req.body;
        const deviceManager = getDeviceManager();

        // Update test status and record results
        await deviceManager.updateDeviceStatus(id, 'test_status', 'IN_PROGRESS', operator_id);

        if (test_results) {
            await deviceManager.recordTestResults(id, test_results);
        }

        logger.info('Device testing started', {
            endpoint: '/api/devices/:id/test',
            device_id: id,
            operator_id,
            has_results: !!test_results
        });

        res.json({
            message: 'Device testing started',
            device_id: id,
            status: 'IN_PROGRESS',
            operator_id,
            test_results: test_results || null,
            timestamp: new Date().toISOString()
        });
    } catch (error) {
        logger.error('Device testing start failed', error);
        res.status(500).json({
            message: 'Device testing start failed',
            error: error.message,
            timestamp: new Date().toISOString()
        });
    }
});

router.get('/devices/:id/status', async (req, res) => {
    try {
        const { id } = req.params;
        const deviceManager = getDeviceManager();
        const device = await deviceManager.getDeviceById(id);

        if (!device) {
            return res.status(404).json({
                message: 'Device not found',
                device_id: id,
                timestamp: new Date().toISOString()
            });
        }

        const status = {
            device_id: id,
            serial_number: device.serial_number,
            programming_status: device.programming_status,
            provisioning_status: device.provisioning_status,
            test_status: device.test_status,
            overall_status: deviceManager.getOverallStatus(device),
            last_updated: device.updated_at
        };

        logger.info('Device status requested', {
            endpoint: '/api/devices/:id/status',
            device_id: id
        });

        res.json({
            message: 'Device status retrieved',
            status: status,
            timestamp: new Date().toISOString()
        });
    } catch (error) {
        logger.error('Device status retrieval failed', error);
        res.status(500).json({
            message: 'Device status retrieval failed',
            error: error.message,
            timestamp: new Date().toISOString()
        });
    }
});

// Database migration endpoints
router.get('/migrations/test', async (req, res) => {
    try {
        const migrations = getDatabaseMigrations();
        await migrations.initialize();
        const result = await migrations.testMigrationSystem();

        logger.info('Migration system test requested', { endpoint: '/api/migrations/test' });

        // Check if request wants HTML format
        if (req.headers.accept && req.headers.accept.includes('text/html')) {
            const integrity = result.results.database_integrity;
            const allChecksPass = integrity.checks.every(check => check.passed);

            let html = `
                <html>
                <head>
                    <title>Database Migration System Test</title>
                    <style>
                        body { font-family: Arial, sans-serif; background: #006E74; color: white; padding: 20px; }
                        .container { max-width: 900px; margin: 0 auto; background: rgba(0,151,172,0.1); padding: 20px; border-radius: 10px; }
                        h1 { color: #0097AC; }
                        .status { background: #0097AC; padding: 15px; border-radius: 5px; margin: 20px 0; }
                        .check { background: rgba(255,255,255,0.1); padding: 10px; margin: 10px 0; border-radius: 5px; }
                        .pass { border-left: 5px solid #00ff00; }
                        .fail { border-left: 5px solid #ff0000; }
                        .migration-info { background: rgba(0,151,172,0.2); padding: 15px; border-radius: 5px; margin: 10px 0; }
                        .back { color: #0097AC; text-decoration: none; }
                        table { width: 100%; border-collapse: collapse; margin: 10px 0; }
                        th, td { padding: 8px; text-align: left; border-bottom: 1px solid #0097AC; }
                        th { background: #0097AC; }
                    </style>
                </head>
                <body>
                    <div class="container">
                        <h1>🔄 Database Migration System Test</h1>

                        <div class="status">
                            <h3>Migration System Status: ✅ ${result.results.migration_system.status}</h3>
                            <p><strong>Current Version:</strong> ${result.results.migration_system.current_version}</p>
                            <p><strong>Migrations Applied:</strong> ${result.results.migration_system.migration_count}</p>
                        </div>

                        <h3>🔍 Database Integrity Checks</h3>
                        <div class="status">
                            <strong>Overall Status: ${allChecksPass ? '✅ HEALTHY' : '⚠️ ISSUES FOUND'}</strong>
                        </div>`;

            integrity.checks.forEach(check => {
                html += `
                        <div class="check ${check.passed ? 'pass' : 'fail'}">
                            <strong>${check.check}:</strong> ${check.passed ? '✅ PASS' : '❌ FAIL'}<br>
                            <small>Expected: ${check.expected} | Got: ${check.actual}</small><br>
                            <small>Details: ${Array.isArray(check.details) ? check.details.join(', ') : check.details}</small>
                        </div>`;
            });

            html += `
                        <div class="migration-info">
                            <h4>📊 System Information</h4>
                            <p><strong>Test Completed:</strong> ${result.results.test_timestamp}</p>
                            <p><strong>Migration System:</strong> Initialized and operational</p>
                            <p><strong>Database Version Control:</strong> Active</p>
                        </div>

                        <p><a href="/" class="back">← Back to Main Page</a></p>
                    </div>
                </body>
                </html>`;

            res.send(html);
        } else {
            res.json(result);
        }

    } catch (error) {
        logger.error('Migration system test failed', error);
        res.status(500).json({
            message: 'Migration system test failed',
            error: error.message,
            timestamp: new Date().toISOString()
        });
    }
});

router.get('/migrations/history', async (req, res) => {
    try {
        const migrations = getDatabaseMigrations();
        await migrations.initialize();
        const history = await migrations.getMigrationHistory();

        logger.info('Migration history requested', { endpoint: '/api/migrations/history' });

        // Check if request wants HTML format
        if (req.headers.accept && req.headers.accept.includes('text/html')) {
            let html = `
                <html>
                <head>
                    <title>Database Migration History</title>
                    <style>
                        body { font-family: Arial, sans-serif; background: #006E74; color: white; padding: 20px; }
                        .container { max-width: 900px; margin: 0 auto; background: rgba(0,151,172,0.1); padding: 20px; border-radius: 10px; }
                        h1 { color: #0097AC; }
                        .status { background: #0097AC; padding: 15px; border-radius: 5px; margin: 20px 0; }
                        table { width: 100%; border-collapse: collapse; margin: 20px 0; }
                        th, td { padding: 10px; text-align: left; border-bottom: 1px solid #0097AC; }
                        th { background: #0097AC; }
                        .success { color: #00ff00; }
                        .failed { color: #ff0000; }
                        .back { color: #0097AC; text-decoration: none; }
                    </style>
                </head>
                <body>
                    <div class="container">
                        <h1>📋 Database Migration History</h1>

                        <div class="status">
                            <h3>Current Database Version: ${history.current_version}</h3>
                            <p><strong>Total Migrations:</strong> ${history.migration_count}</p>
                        </div>

                        <table>
                            <thead>
                                <tr>
                                    <th>Version</th>
                                    <th>Migration Name</th>
                                    <th>Applied Date</th>
                                    <th>Execution Time</th>
                                    <th>Status</th>
                                </tr>
                            </thead>
                            <tbody>`;

            history.migrations.forEach(migration => {
                html += `
                                <tr>
                                    <td><strong>${migration.version}</strong></td>
                                    <td>${migration.name}</td>
                                    <td>${new Date(migration.applied_at).toLocaleString()}</td>
                                    <td>${migration.execution_time_ms}ms</td>
                                    <td class="${migration.status.toLowerCase()}">${migration.status}</td>
                                </tr>`;
            });

            html += `
                            </tbody>
                        </table>

                        <p><a href="/" class="back">← Back to Main Page</a></p>
                    </div>
                </body>
                </html>`;

            res.send(html);
        } else {
            res.json(history);
        }

    } catch (error) {
        logger.error('Migration history request failed', error);
        res.status(500).json({
            message: 'Migration history request failed',
            error: error.message,
            timestamp: new Date().toISOString()
        });
    }
});

// Enhanced statistics endpoints
router.get('/statistics/daily', async (req, res) => {
    try {
        const { date } = req.query;
        const targetDate = date || new Date().toISOString().split('T')[0];
        const deviceManager = getDeviceManager();

        const stats = await deviceManager.getDailyStatistics(targetDate);

        logger.info('Daily statistics requested', {
            endpoint: '/api/statistics/daily',
            date: targetDate
        });

        res.json({
            message: 'Daily statistics retrieved',
            date: targetDate,
            statistics: stats,
            timestamp: new Date().toISOString()
        });
    } catch (error) {
        logger.error('Daily statistics retrieval failed', error);
        res.status(500).json({
            message: 'Daily statistics retrieval failed',
            error: error.message,
            timestamp: new Date().toISOString()
        });
    }
});

router.get('/statistics/operator', async (req, res) => {
    try {
        const { operator_id, date_from, date_to } = req.query;
        const deviceManager = getDeviceManager();

        const stats = await deviceManager.getOperatorStatistics(operator_id, date_from, date_to);

        logger.info('Operator statistics requested', {
            endpoint: '/api/statistics/operator',
            operator_id,
            date_from,
            date_to
        });

        res.json({
            message: 'Operator statistics retrieved',
            operator_id,
            date_range: { from: date_from, to: date_to },
            statistics: stats,
            timestamp: new Date().toISOString()
        });
    } catch (error) {
        logger.error('Operator statistics retrieval failed', error);
        res.status(500).json({
            message: 'Operator statistics retrieval failed',
            error: error.message,
            timestamp: new Date().toISOString()
        });
    }
});

router.get('/statistics/yield', async (req, res) => {
    try {
        const { date_from, date_to, region } = req.query;
        const deviceManager = getDeviceManager();

        const yieldStats = await deviceManager.getYieldStatistics(date_from, date_to, region);

        logger.info('Yield statistics requested', {
            endpoint: '/api/statistics/yield',
            date_from,
            date_to,
            region
        });

        res.json({
            message: 'Yield statistics retrieved',
            date_range: { from: date_from, to: date_to },
            region: region || 'all',
            yield_statistics: yieldStats,
            timestamp: new Date().toISOString()
        });
    } catch (error) {
        logger.error('Yield statistics retrieval failed', error);
        res.status(500).json({
            message: 'Yield statistics retrieval failed',
            error: error.message,
            timestamp: new Date().toISOString()
        });
    }
});

// API Documentation endpoint
router.get('/docs', (req, res) => {
    try {
        logger.info('API documentation requested', { endpoint: '/api/docs' });

        const html = `
            <html>
            <head>
                <title>COLDRA Factory Tool - API Documentation</title>
                <style>
                    body { font-family: Arial, sans-serif; background: #006E74; color: white; padding: 20px; }
                    .container { max-width: 1000px; margin: 0 auto; background: rgba(0,151,172,0.1); padding: 20px; border-radius: 10px; }
                    h1 { color: #0097AC; }
                    h2 { color: #0097AC; border-bottom: 2px solid #0097AC; padding-bottom: 10px; }
                    .endpoint { background: rgba(255,255,255,0.1); padding: 15px; margin: 15px 0; border-radius: 5px; }
                    .method-get { border-left: 5px solid #00ff00; }
                    .method-post { border-left: 5px solid #ff9900; }
                    .method { font-weight: bold; color: #0097AC; }
                    .url { font-family: monospace; background: rgba(0,0,0,0.3); padding: 5px; border-radius: 3px; }
                    .description { margin: 10px 0; }
                    .example { background: rgba(0,0,0,0.3); padding: 10px; border-radius: 3px; font-family: monospace; font-size: 12px; }
                    .back { color: #0097AC; text-decoration: none; }
                    .section { margin: 30px 0; }
                </style>
            </head>
            <body>
                <div class="container">
                    <h1>📚 COLDRA Factory Tool - API Documentation</h1>
                    <p>Complete API reference for the COLDRA Factory Tool manufacturing system.</p>

                    <div class="section">
                        <h2>🔧 System Endpoints (3 endpoints)</h2>

                        <div class="endpoint method-get">
                            <div class="method">1. GET</div>
                            <div class="url">/api/health</div>
                            <div class="description">System health check</div>
                        </div>

                        <div class="endpoint method-get">
                            <div class="method">2. GET</div>
                            <div class="url">/api/status</div>
                            <div class="description">Application status information</div>
                        </div>

                        <div class="endpoint method-get">
                            <div class="method">3. GET</div>
                            <div class="url">/api/version</div>
                            <div class="description">Version information</div>
                        </div>
                    </div>

                    <div class="section">
                        <h2>🗄️ Database Endpoints (3 endpoints)</h2>

                        <div class="endpoint method-get">
                            <div class="method">4. GET</div>
                            <div class="url">/api/database/test</div>
                            <div class="description">Test database connection</div>
                        </div>

                        <div class="endpoint method-get">
                            <div class="method">5. GET</div>
                            <div class="url">/api/database/schema/check</div>
                            <div class="description">Check database schema status</div>
                        </div>

                        <div class="endpoint method-post">
                            <div class="method">6. POST</div>
                            <div class="url">/api/database/schema/create</div>
                            <div class="description">Create database schema (setup only)</div>
                            <div class="example">curl -X POST http://localhost:3000/api/database/schema/create</div>
                        </div>
                    </div>

                    <div class="section">
                        <h2>🔢 Serial Number Endpoints (3 endpoints)</h2>

                        <div class="endpoint method-get">
                            <div class="method">7. GET</div>
                            <div class="url">/api/serial/test</div>
                            <div class="description">Test serial number generation system</div>
                        </div>

                        <div class="endpoint method-post">
                            <div class="method">8. POST</div>
                            <div class="url">/api/serial/generate</div>
                            <div class="description">Generate new serial number</div>
                            <div class="example">curl -X POST http://localhost:3000/api/serial/generate \\<br>
  -H "Content-Type: application/json" \\<br>
  -d '{"region":"US915"}'</div>
                        </div>

                        <div class="endpoint method-post">
                            <div class="method">9. POST</div>
                            <div class="url">/api/serial/validate</div>
                            <div class="description">Validate serial number format</div>
                            <div class="example">curl -X POST http://localhost:3000/api/serial/validate \\<br>
  -H "Content-Type: application/json" \\<br>
  -d '{"serial_number":"070925-U-001"}'</div>
                        </div>
                    </div>

                    <div class="section">
                        <h2>📱 Device Management Endpoints (7 endpoints)</h2>

                        <div class="endpoint method-get">
                            <div class="method">10. GET</div>
                            <div class="url">/api/devices/test</div>
                            <div class="description">Test device record management system</div>
                        </div>

                        <div class="endpoint method-get">
                            <div class="method">11. GET</div>
                            <div class="url">/api/devices/stats</div>
                            <div class="description">Get device production statistics</div>
                        </div>

                        <div class="endpoint method-post">
                            <div class="method">12. POST</div>
                            <div class="url">/api/devices/create</div>
                            <div class="description">Create new device record</div>
                            <div class="example">curl -X POST http://localhost:3000/api/devices/create \\<br>
  -H "Content-Type: application/json" \\<br>
  -d '{"serial_number":"070925-U-001","deveui":"1234567890ABCDEF","region":"US915"}'</div>
                        </div>

                        <div class="endpoint method-get">
                            <div class="method">13. GET</div>
                            <div class="url">/api/devices/:id</div>
                            <div class="description">Get device by ID</div>
                        </div>

                        <div class="endpoint method-put">
                            <div class="method">14. PUT</div>
                            <div class="url">/api/devices/:id</div>
                            <div class="description">Update device record</div>
                            <div class="example">curl -X PUT http://localhost:3000/api/devices/1 \\<br>
  -H "Content-Type: application/json" \\<br>
  -d '{"firmware_version":"1.2.0","operator_id":"OP001"}'</div>
                        </div>

                        <div class="endpoint method-get">
                            <div class="method">15. DELETE</div>
                            <div class="url">/api/devices/:id</div>
                            <div class="description">Delete device record</div>
                        </div>

                        <div class="endpoint method-get">
                            <div class="method">16. GET</div>
                            <div class="url">/api/devices/search</div>
                            <div class="description">Search devices by criteria</div>
                            <div class="example">GET /api/devices/search?serial_number=070925&region=US915</div>
                        </div>
                    </div>

                    <div class="section">
                        <h2>🏭 Production Workflow Endpoints (4 endpoints)</h2>

                        <div class="endpoint method-post">
                            <div class="method">17. POST</div>
                            <div class="url">/api/devices/:id/program</div>
                            <div class="description">Start device firmware programming</div>
                            <div class="example">curl -X POST http://localhost:3000/api/devices/1/program \\<br>
  -H "Content-Type: application/json" \\<br>
  -d '{"firmware_version":"1.2.0","operator_id":"OP001"}'</div>
                        </div>

                        <div class="endpoint method-post">
                            <div class="method">18. POST</div>
                            <div class="url">/api/devices/:id/provision</div>
                            <div class="description">Start device credential provisioning</div>
                            <div class="example">curl -X POST http://localhost:3000/api/devices/1/provision \\<br>
  -H "Content-Type: application/json" \\<br>
  -d '{"operator_id":"OP001"}'</div>
                        </div>

                        <div class="endpoint method-post">
                            <div class="method">19. POST</div>
                            <div class="url">/api/devices/:id/test</div>
                            <div class="description">Start device testing</div>
                            <div class="example">curl -X POST http://localhost:3000/api/devices/1/test \\<br>
  -H "Content-Type: application/json" \\<br>
  -d '{"operator_id":"OP001","test_results":{"join_test":"PASS"}}'</div>
                        </div>

                        <div class="endpoint method-get">
                            <div class="method">20. GET</div>
                            <div class="url">/api/devices/:id/status</div>
                            <div class="description">Get current device status</div>
                        </div>
                    </div>

                    <div class="section">
                        <h2>📊 Statistics Endpoints (3 endpoints)</h2>

                        <div class="endpoint method-get">
                            <div class="method">21. GET</div>
                            <div class="url">/api/statistics/daily</div>
                            <div class="description">Get daily production statistics</div>
                            <div class="example">GET /api/statistics/daily?date=2025-09-07</div>
                        </div>

                        <div class="endpoint method-get">
                            <div class="method">22. GET</div>
                            <div class="url">/api/statistics/operator</div>
                            <div class="description">Get operator performance statistics</div>
                            <div class="example">GET /api/statistics/operator?operator_id=OP001&date_from=2025-09-01</div>
                        </div>

                        <div class="endpoint method-get">
                            <div class="method">23. GET</div>
                            <div class="url">/api/statistics/yield</div>
                            <div class="description">Get production yield statistics</div>
                            <div class="example">GET /api/statistics/yield?date_from=2025-09-01&region=US915</div>
                        </div>
                    </div>

                    <div class="section">
                        <h2>🔍 Validation & Migration Endpoints (3 endpoints)</h2>

                        <div class="endpoint method-get">
                            <div class="method">24. GET</div>
                            <div class="url">/api/validation/test</div>
                            <div class="description">Test data validation system</div>
                        </div>

                        <div class="endpoint method-get">
                            <div class="method">25. GET</div>
                            <div class="url">/api/migrations/test</div>
                            <div class="description">Test database migration system</div>
                        </div>

                        <div class="endpoint method-get">
                            <div class="method">26. GET</div>
                            <div class="url">/api/migrations/history</div>
                            <div class="description">View database migration history</div>
                        </div>
                    </div>

                    <div class="section">
                        <h2>📋 Data Formats</h2>

                        <div class="endpoint">
                            <strong>Serial Number Format:</strong> DDMMYY-R-NNN<br>
                            <small>DD=Day, MM=Month, YY=Year, R=Region(U/E/I), NNN=Counter</small><br>
                            <strong>Example:</strong> 070925-U-001
                        </div>

                        <div class="endpoint">
                            <strong>DevEUI Format:</strong> 16 hexadecimal characters<br>
                            <strong>Example:</strong> 1234567890ABCDEF
                        </div>

                        <div class="endpoint">
                            <strong>AppKey Format:</strong> 32 hexadecimal characters<br>
                            <strong>Example:</strong> 00112233445566778899AABBCCDDEEFF
                        </div>

                        <div class="endpoint">
                            <strong>Regions:</strong> US915, EU868, IN865
                        </div>
                    </div>

                    <p><a href="/" class="back">← Back to Main Page</a></p>
                </div>
            </body>
            </html>`;

        res.send(html);

    } catch (error) {
        logger.error('API documentation request failed', error);
        res.status(500).json({
            message: 'API documentation request failed',
            error: error.message,
            timestamp: new Date().toISOString()
        });
    }
});

// Validation test endpoint
router.get('/validation/test', ValidationMiddleware.testValidation);

// Test endpoint for development
router.get('/test', (req, res) => {
    logger.info('API test endpoint accessed', { ip: req.ip });
    res.json({
        message: 'API test endpoint working',
        timestamp: new Date().toISOString(),
        request_info: {
            method: req.method,
            path: req.path,
            query: req.query,
            headers: {
                'user-agent': req.get('User-Agent'),
                'content-type': req.get('Content-Type')
            }
        }
    });
});

module.exports = router;

#!/usr/bin/env python3
"""
Test the frontend configuration interface by simulating user actions
"""

import requests
import json
import time

def test_frontend_configuration():
    print('=== TESTING FRONTEND CONFIGURATION WORKFLOW ===')

    # Step 1: Reset to known state
    print('1. Resetting configuration to defaults...')
    reset_response = requests.post('http://localhost:8001/reset-configuration')
    if reset_response.status_code == 200:
        print('✅ Configuration reset successful')
    else:
        print('❌ Configuration reset failed')
        return False

    # Step 2: Get initial battery life
    print('\n2. Getting initial battery life...')
    calc_response = requests.post('http://localhost:8001/calculate-battery-life', json={
        'temperature': 20.0,
        'tx_power_dbm': 14,
        'tx_interval_minutes': 15,
        'spreading_factor': 'SF7',
        'sleep_mode': 'stop2',
        'sensor_resolution_bits': 12,
        'measurements_per_tx': 1,
        'led_enabled': True,
        'battery_count': 2
    })
    
    if calc_response.status_code == 200:
        initial_result = calc_response.json()
        initial_battery_life = initial_result['battery_life_years']
        initial_tx_current = initial_result['lora_details']['tx_current_ma']
        print(f'✅ Initial battery life: {initial_battery_life} years')
        print(f'✅ Initial TX current: {initial_tx_current} mA')
    else:
        print('❌ Initial calculation failed')
        return False

    # Step 3: Simulate user changing configuration (like frontend would do)
    print('\n3. Simulating user configuration changes...')
    user_config_changes = {
        'lora_config': {
            'tx_current_14dbm': 35.0,  # User increases TX current
            'sleep_current_stop2': 3.0,  # User increases sleep current
            'tx_temp_coefficient': 0.0,  # User sets no temperature effect
            'sleep_temp_coefficient': 0.0
        },
        'sensor_config': {
            'active_current_ma': 2.0,  # User increases sensor current
            'standby_current_ua': 1.0
        }
    }
    
    save_response = requests.post('http://localhost:8001/save-configuration', json=user_config_changes)
    if save_response.status_code == 200:
        print('✅ User configuration changes saved')
        saved_config = save_response.json()['configuration']
        print(f'   New TX 14dBm current: {saved_config["lora_config"]["tx_current_14dbm"]} mA')
        print(f'   New sleep current: {saved_config["lora_config"]["sleep_current_stop2"]} µA')
        print(f'   New sensor active current: {saved_config["sensor_config"]["active_current_ma"]} mA')
    else:
        print('❌ Configuration save failed')
        return False

    # Step 4: Get new battery life after configuration changes
    print('\n4. Getting battery life after configuration changes...')
    calc_response = requests.post('http://localhost:8001/calculate-battery-life', json={
        'temperature': 20.0,
        'tx_power_dbm': 14,
        'tx_interval_minutes': 15,
        'spreading_factor': 'SF7',
        'sleep_mode': 'stop2',
        'sensor_resolution_bits': 12,
        'measurements_per_tx': 1,
        'led_enabled': True,
        'battery_count': 2
    })
    
    if calc_response.status_code == 200:
        new_result = calc_response.json()
        new_battery_life = new_result['battery_life_years']
        new_tx_current = new_result['lora_details']['tx_current_ma']
        new_sensor_current = new_result['sensor_details']['active_current_ma']
        
        print(f'✅ New battery life: {new_battery_life} years')
        print(f'✅ New TX current: {new_tx_current} mA (should be 35.0)')
        print(f'✅ New sensor current: {new_sensor_current} mA (should be 2.0)')
        
        # Verify changes took effect
        if abs(new_tx_current - 35.0) < 0.01:
            print('✅ TX current change verified!')
        else:
            print('❌ TX current change NOT applied')
            
        if abs(new_sensor_current - 2.0) < 0.01:
            print('✅ Sensor current change verified!')
        else:
            print('❌ Sensor current change NOT applied')
            
        # Check if battery life changed
        if abs(new_battery_life - initial_battery_life) > 1.0:
            print(f'✅ Battery life changed significantly: {initial_battery_life:.1f} → {new_battery_life:.1f} years')
        else:
            print('❌ Battery life did not change significantly')
            
    else:
        print('❌ New calculation failed')
        return False

    # Step 5: Test configuration persistence
    print('\n5. Testing configuration persistence...')
    get_response = requests.get('http://localhost:8001/get-configuration')
    if get_response.status_code == 200:
        persisted_config = get_response.json()['configuration']
        persisted_tx_current = persisted_config['lora_config']['tx_current_14dbm']
        persisted_sensor_current = persisted_config['sensor_config']['active_current_ma']
        
        if abs(persisted_tx_current - 35.0) < 0.01 and abs(persisted_sensor_current - 2.0) < 0.01:
            print('✅ Configuration properly persisted!')
        else:
            print('❌ Configuration not properly persisted')
    else:
        print('❌ Failed to retrieve persisted configuration')

    print('\n🎉 Frontend configuration workflow test completed!')
    return True

if __name__ == "__main__":
    success = test_frontend_configuration()
    if success:
        print('\n✅ All tests passed - configuration system working properly!')
    else:
        print('\n❌ Some tests failed - configuration system needs fixes!')
